# Copilot Instructions for web-yaqeen

## Overview
The `web-yaqeen` project is a Next.js application with a modular architecture. It uses TypeScript for strict typing and follows modern React patterns, including server components and server actions. The project is structured into distinct domains such as `api`, `app`, `components`, `lib`, and `i18n`, each serving a specific purpose.

## Key Directories and Files
- **`packages/yaqeen/api`**: Contains API contracts, types, and schemas. Zod is used for validation.
- **`packages/yaqeen/app`**: Houses the Next.js app router structure, including server components and layouts.
- **`packages/yaqeen/components`**: Reusable UI components, adhering to the shadcnui design system.
- **`packages/yaqeen/i18n`**: Manages internationalization with scripts for downloading and uploading translations.
- **`packages/yaqeen/lib`**: Utility functions and services for shared logic.

## Developer Workflows
### Build and Development
- Start development server: `npm run dev`
- Build for production: `npm run build`
- Start production server: `npm run start`

### Code Quality
- Lint code: `npm run lint`
- Fix linting issues: `npm run lint:fix`
- Check formatting: `npm run format:check`
- Format code: `npm run format:write`

### Testing
- Run tests: `npm run test`

### Internationalization
- Download translations: `npm run i18n:download`
- Upload translations: `npm run i18n:upload`

## Project-Specific Conventions
- **TypeScript**: Strict typing enforced via `tsconfig.json`.
- **Validation**: Use Zod for schemas.
- **State Management**: Use Jotai for client state; avoid Redux or Context.
- **Exports**: Prefer named exports and use barrel exports where appropriate.
- **File Naming**: Follow kebab-case.
- **UI Components**: Use shadcnui and ensure proper TypeScript typing.

## Integration Points
- **API Contracts**: Defined in `packages/yaqeen/api/contracts`.
- **Server Actions**: Used for data mutations in the app router.
- **i18n**: Scripts for managing translations are in `packages/yaqeen/i18n`.

## Examples
- **Validation**: Refer to `packages/yaqeen/api/zod-types.ts` for Zod schemas.
- **State Management**: See `packages/yaqeen/app/(portal)/store.ts` for Jotai usage.
- **UI Components**: Check `packages/yaqeen/components/tooltip-component.tsx` for shadcnui examples.

## Notes
- Keep file sizes small; extract components when necessary.
- Use React Server Components where possible to optimize performance.

For more details, refer to the `README.md` or specific files in the codebase.
