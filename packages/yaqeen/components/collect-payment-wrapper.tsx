"use client";

import React, { useState, type ReactElement } from "react";
import { Loader2 } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

import PaymentFlow from "@/app/(portal)/rental/branches/[id]/bookings/[bookingId]/payment/_components/PaymentFlow";
import { useBooking } from "@/lib/hooks/useBooking";
import { useGetPos } from "@/lib/hooks/useGetPos";
import { Button } from "./ui/button";
import { SaveRemainingAmount } from "@/app/(portal)/rental/branches/[id]/bookings/_components/SaveRemainingAmount";

interface CollectPaymentWrapperProps {
  bookingId: string;
  children: ReactElement;
  branchId: number;
  remainingAmount?: number;
}

export function CollectPaymentWrapper({ bookingId, children, branchId,remainingAmount }: CollectPaymentWrapperProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const pathname = usePathname();
  const router = useRouter();

  const { data: bookingData, isLoading: bookingLoading } = useBooking(bookingId);
  const { data: posData, isLoading: posLoading } = useGetPos({ branchId, enabled: isOpen });

  const isDataLoading = isOpen && (bookingLoading || posLoading);

  const isOnBookingDetailsPage = () => {
    const pattern = /^\/.*\/rental\/branches\/\d+\/bookings\/\d+$/;
    return pattern.test(pathname);
  };

  return (
    <>
      <Button
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();

          setIsOpen(true);
        }}
        variant='ghost'
        className="flex items-center justify-between p-0 h-auto w-full"
      >
        {children}
        {isDataLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
        
      </Button>
        <SaveRemainingAmount amount={Number(remainingAmount).toFixed(2)} />
      {isOpen && !isDataLoading && bookingData && posData?.data && (
        <div>
          <PaymentFlow
            posResponse={posData}
            bookingId={Number(bookingId)}
            paymentModalOpen={true}
            isValidateCodePage={false}
            routePath={`/rental/branches/${branchId}/bookings/${bookingId}`}
            setPaymentModalOpen={(_, buttonType) => {
              if (!isOnBookingDetailsPage() && buttonType === "isPayment") {
                router.push(`/rental/branches/${branchId}/bookings/${bookingId}`);
              } else {
                setIsOpen(false);
              }
            }}
            loading={loading}
            bookingNo={bookingData.bookingNo}
            setLoading={setLoading}
            booking={bookingData}
          />
        </div>
      )}
    </>
  );
}
