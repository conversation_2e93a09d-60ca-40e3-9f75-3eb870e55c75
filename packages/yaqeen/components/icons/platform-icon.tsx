"use client";

import clsx from "clsx";
import { LumiLemon } from "./LumiLemon";
import { type Platform } from "../app-sidebar/content.mock";
import { useSidebar } from "../ui/sidebar";

export default function PlatformIcon({ variant, className }: { variant: Platform["id"]; className?: string }) {
  const { state } = useSidebar();
  const sizeClasses = state === "collapsed" ? "h-8 w-8 p-1" : "h-10 w-10 p-2";

  const fill = {
    rental: "#3E5B0C", // text-lumi-900
    admin: "#7C3612", // text-orange-900
    fleet: "#1E3A8A", // text-blue-900
    lease: "#7C3612", // text-red-900
  };
  return (
    <div
      className={clsx(
        "flex items-center justify-center rounded-lg border",
        sizeClasses,
        {
          "text-lumi-900 border-lumi-400 bg-lumi-400": variant === "rental",
          "border-orange-100 bg-orange-50 text-orange-900": variant === "admin",
          "border-blue-200 bg-blue-100 text-blue-900": variant === "fleet",
          "border-red-200 bg-red-100 text-red-900": variant === "lease",
        },
        className
      )}
    >
      <LumiLemon fill={fill[variant]} />
    </div>
  );
}
