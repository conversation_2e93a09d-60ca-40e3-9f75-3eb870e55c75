"use client";

import TableScroller from "@/components/ui/data-table/table-scroller";
import { Table, TableBody, TableCell, TableFooter, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { usePagination } from "@/lib/hooks/usePagination";
import { useCheckDevice } from "@/lib/hooks/useCheckDevice";
import { Binoculars, CalendarX } from "@phosphor-icons/react/dist/ssr";
import {
  type Column,
  type ColumnDef,
  type ColumnFiltersState,
  type SortingState,
  type VisibilityState,
  type Table as TableType,
  flexRender,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import clsx from "clsx";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React, { type CSSProperties, type ReactElement, startTransition, useEffect, useState } from "react";
import { But<PERSON> } from "../button";
import NoData from "./no-data";
import { PaginationControlsWithServer } from "./pagination-controls";
import { SearchInput } from "./search-input";
import { DataTableToolbar, type FilterOption } from "./toolbar";
import { useProgressBar } from "@/components/progress-bar";
import { cn, trackEvent } from "@/lib/utils";
import { type Route } from "next";

interface Option {
  label: string;
  value: string;
}
interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: { total: number; data: TData[] };
  searchPlaceholder?: string;
  searchSuggestions?: string[] | null;
  paginationEnabled?: boolean;
  filters?: FilterOption[];
  rowClickId?: string | string[];
  rowClickPath?: string;
  countText?: string;
  searchFilters?: Option[];
  singleSearchFilter?: Option;
  emptyMessage?: string | ((table: TableType<TData>) => ReactElement);
  baseRedirectPath?: string;
  styleClasses?: {
    wrapper?: string;
  };
  columnVisibility?: VisibilityState;
  onDataChange?: (data: TData[]) => void;
  footerRow?: ((table: TableType<TData>) => ReactElement) | null;
  clientPagination?: boolean;
  pageSize?: number;
  legacyPage?: boolean;
  extraParams?: string[];
  leftPinnedColumn?: boolean;
  onRowSelectionChange?: (selected: Record<string, boolean>) => void;
  rowSelection?: Record<string, boolean>;
  uniqueId?: string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  searchPlaceholder = "",
  searchSuggestions,
  paginationEnabled = true,
  filters = [],
  countText,
  rowClickId,
  rowClickPath,
  searchFilters = [],
  singleSearchFilter,
  emptyMessage = "No Results Found",
  baseRedirectPath,
  styleClasses,
  columnVisibility,
  extraParams = [],
  footerRow,
  onDataChange,
  legacyPage = false,
  clientPagination = false,
  leftPinnedColumn = true,
  onRowSelectionChange,
  rowSelection,
  uniqueId,
}: DataTableProps<TData, TValue>) {
  const progress = useProgressBar();
  const isBelow = useCheckDevice("lg");
  const [sorting, setSorting] = useState<SortingState>([]);
  const [originalData, setOriginalData] = useState([...data.data]);
  const [localData, setLocalData] = useState<TData[]>([...data.data]);
  const [editedRows, setEditedRows] = useState({});
  const [localColumnVisibility, setLocalColumnVisibility] = useState<VisibilityState>(
    columnVisibility ?? {
      id: false,
      longitude: false,
      latitude: false,
      trackId: false,
      preferredVehicleGroup: false,
      paymentStatus: false,
    }
  );
  const [{ pageNumber, pageSize }, setPaginationState] = usePagination({ legacyPage });
  const searchParams = useSearchParams();
  const params = Array.from(searchParams.entries());
  const filterKeyMap = new Map(filters.map((f) => [f.filterKey, f]));
  const [internalRowSelection, setInternalRowSelection] = useState<Record<string, boolean>>({});
  const [currentSearchFilter, setCurrentSearchFilter] = useState<string>("");
  const [currentSearchKeyword, setCurrentSearchKeyword] = useState<string>("");
  const resolvedRowSelection = rowSelection ?? internalRowSelection;

  const initialColumnFilters: ColumnFiltersState = params
    .map(([id, value]) => {
      const filter = filterKeyMap.get(id);
      if (filter) {
        return {
          id: filter.columnKey,
          value: value.split(","),
        };
      }
      return null;
    })
    .filter((f): f is { id: string; value: string[] } => Boolean(f));

  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>(initialColumnFilters);

  const filterKeys = filters.map((filter) => filter.filterKey);
  const searchFilterKeys = searchFilters.map((filter) => filter.value);

  let hasQueryParams = false;
  if (searchFilterKeys || filterKeys) {
    hasQueryParams = [...searchFilters, ...filterKeys].some((filter) => params.some(([key]) => key === filter));
  }

  useEffect(() => {
    setLocalData([...data.data]);
  }, [data.data]);

  useEffect(() => {
    if (currentSearchFilter && currentSearchKeyword) {
      // trigger event after search result is there
      trackEvent("Search Used", {
        search_type: currentSearchFilter,
        search_keyword: currentSearchKeyword,
        result_count: data?.total,
      });
    } else {
      setCurrentSearchFilter("");
      setCurrentSearchKeyword("");
    }
  }, [localData]);

  const toggleEdit = (rowIndex: number) => {
    setEditedRows((old: Record<string, TData>) => ({
      ...old,
      [rowIndex]: !old[rowIndex],
    }));
  };

  const table = useReactTable({
    initialState: {
      columnPinning: {
        ...(leftPinnedColumn && { left: ["bookingNo"] }),
        right: ["actions"],
      },
    },
    enableRowSelection: true,
    data: clientPagination ? localData : data.data,
    columns,
    manualPagination: !clientPagination,
    manualFiltering: true,
    pageCount: clientPagination ? Math.ceil(localData.length / pageSize) : Math.ceil(data.total / pageSize),
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnVisibilityChange: setLocalColumnVisibility,
    onColumnFiltersChange: setColumnFilters,
    onPaginationChange: (pageStateFn) => {
      if (typeof pageStateFn === "function") {
        const { pageIndex, pageSize: newPageSize } = pageStateFn({
          pageIndex: pageNumber,
          pageSize: pageSize,
        });
        if (pageIndex === pageNumber && newPageSize === pageSize) {
          return;
        }
        setPaginationState({ pageNumber: pageIndex, pageSize: newPageSize });
      }
    },
    onRowSelectionChange: (updater) => {
      const newSelection = typeof updater === "function" ? updater(resolvedRowSelection) : updater;
      if (rowSelection) {
        onRowSelectionChange?.(newSelection);
      } else {
        setInternalRowSelection(newSelection);
      }
    },
    state: {
      sorting,
      columnVisibility: localColumnVisibility,
      columnFilters,
      pagination: {
        pageSize,
        pageIndex: pageNumber,
      },
      rowSelection: resolvedRowSelection,
    },
    getRowId: (row: TData) =>
      uniqueId ? (row[uniqueId as keyof TData] as string) : (row["id" as keyof TData] as string),
    meta: {
      editedRows,
      setEditedRows,
      toggleEdit,
      localData,
      revertData: (rowIndex) => {
        toggleEdit(rowIndex);
        setLocalData(originalData);
        onDataChange?.(originalData);
      },
      updateTempData: (rowIndex: number, columnId: string, value: string | number) => {
        const newData = localData.map((row, index) => {
          if (index === rowIndex) {
            return {
              ...localData[rowIndex],
              [columnId]: value,
            } as TData;
          }
          return row;
        });

        setLocalData(newData);
      },
      updateData: (rowIndex) => {
        setOriginalData(localData);
        onDataChange?.(localData);
        toggleEdit(rowIndex);
      },
      removeRow: (rowIndex: number) => {
        const temp = [...localData];
        temp.splice(rowIndex, 1);
        setLocalData(temp);
        setOriginalData(temp);
        onDataChange?.(temp);
      },
      addRow: () => {
        const newRow = table.getAllLeafColumns().reduce((acc, el) => {
          if (el.id === "edit") return acc;
          return {
            ...acc,
            [el.id]: null,
          };
        }, {});
        const newData = [...localData, newRow] as TData[];
        setLocalData(newData);
        setOriginalData(localData);
        onDataChange?.(newData);
        toggleEdit(newData.length - 1);
      },
    },
  });
  const showPagination = paginationEnabled && (clientPagination ? localData.length > 10 : data.total > 10);
  const total = clientPagination ? localData.length : data.total;
  const start = (pageNumber - (legacyPage ? 1 : 0)) * pageSize;
  const end = Math.min((pageNumber + (legacyPage ? 0 : 1)) * pageSize, total);
  const showScroll = end - start > 10;
  const router = useRouter();
  const pathname = usePathname();

  const getCommonPinningStyles = (column: Column<TData>): CSSProperties => {
    if (!isBelow) return {};
    const isPinned = column.getIsPinned();
    const isLastLeftPinnedColumn = isPinned === "left" && column.getIsLastColumn("left");
    const isFirstRightPinnedColumn = isPinned === "right" && column.getIsFirstColumn("right");

    return {
      boxShadow:
        isBelow && isLastLeftPinnedColumn
          ? "-4px 0 4px -4px gray inset"
          : isBelow && isFirstRightPinnedColumn
            ? "4px 0 4px -4px gray inset"
            : undefined,
      left: isPinned === "left" ? `${column.getStart("left")}px` : undefined,
      right: isPinned === "right" ? `${column.getAfter("right")}px` : undefined,
      position: isPinned ? "sticky" : "relative",
      width: column.getSize(),
      zIndex: isPinned ? 1 : 0,
      backgroundColor: isPinned ? "white" : undefined,
    };
  };

  const onSearchEvent = (type: string, keyword: string) => {
    setCurrentSearchFilter(type);
    setCurrentSearchKeyword(keyword);
  };

  return (
    <React.Fragment>
      {showScroll && <TableScroller />}
      <div className="">
        {filters.length || searchPlaceholder ? (
          <DataTableToolbar className="my-6" table={table} filters={filters}>
            <SearchInput
              placeholder={searchPlaceholder}
              searchFilters={searchFilters}
              singleSearchFilter={singleSearchFilter}
              suggestions={searchSuggestions}
              isTableEmpty={data?.data?.length === 0}
              onSearchEvent={onSearchEvent}
            />
          </DataTableToolbar>
        ) : (
          <></>
        )}
        {data?.data?.length ? (
          <>
            {countText && <p className="my-6 font-medium">{countText}</p>}
            <div className={cn("relative w-full rounded-md border max-2xl:overflow-x-scroll", styleClasses?.wrapper)}>
              <Table>
                <TableHeader>
                  {table.getHeaderGroups().map((headerGroup) => (
                    <TableRow key={`header-${headerGroup.id}`}>
                      {headerGroup.headers.map((header) => {
                        return (
                          <TableHead
                            style={{ ...getCommonPinningStyles(header.column) }}
                            colSpan={header.colSpan}
                            className=" first-of-type:rounded-tl-md last-of-type:rounded-tr-md"
                            key={`header-id${header.id}`}
                          >
                            <span className="block bg-slate-50 bg-opacity-75 backdrop-blur backdrop-filter">
                              {flexRender(header.column.columnDef.header, header.getContext())}
                            </span>
                          </TableHead>
                        );
                      })}
                    </TableRow>
                  ))}
                </TableHeader>
                <TableBody>
                  {table?.getRowModel()?.rows?.length ? (
                    table.getRowModel().rows.map((row, rowIndex) => (
                      <TableRow
                        key={row.id}
                        className={clsx("group hover:bg-slate-50", {
                          "cursor-pointer": rowClickId,
                        })}
                        data-state={row.getIsSelected() && "selected"}
                        onClick={(e) => {
                          if (!rowClickId) return;

                          e.preventDefault();

                          const getValue = (id: string): string =>
                            row?.getValue(id) ?? (row?.original as Record<string, string>)?.[id] ?? "";

                          const values = Array.isArray(rowClickId) ? rowClickId.map(getValue) : getValue(rowClickId);
                          const [basePath, existingQuery] = (baseRedirectPath ?? pathname).split("?");
                          let finalPath = basePath!;

                          if (rowClickPath) {
                            finalPath += `/${rowClickPath}`;

                            if (Array.isArray(rowClickId) && Array.isArray(values)) {
                              rowClickId.forEach((id, idx) => {
                                finalPath = finalPath.replace(`[${id}]`, values[idx] ?? "");
                              });
                            } else if (!Array.isArray(rowClickId)) {
                              finalPath = finalPath.replace(`[${rowClickId}]`, values as string);
                            }
                          } else {
                            finalPath += `/${Array.isArray(values) ? values.join("/") : values}`;
                          }

                          const queryParams = new URLSearchParams();

                          if (existingQuery) {
                            const existingParams = new URLSearchParams(existingQuery);
                            existingParams.forEach((value, key) => queryParams.set(key, value));
                          }

                          if (extraParams.length > 0) {
                            extraParams.forEach((param) => {
                              const val = (row?.original as Record<string, string>)?.[param];
                              if (val !== undefined && val !== null) {
                                queryParams.set(param, val);
                              }
                            });
                          }

                          const fullPath = `${finalPath}${queryParams.toString() ? `?${queryParams.toString()}` : ""}`;

                          progress.start();
                          startTransition(() => {
                            router.push(fullPath as Route);
                            progress.done();
                          });
                        }}
                      >
                        {row.getVisibleCells().map((cell) => (
                          <TableCell
                            className={clsx("border-b-0", {
                              "border-b": rowIndex !== table.getRowModel().rows.length - 1,
                            })}
                            key={`cell-${cell.id}`}
                            style={{ ...getCommonPinningStyles(cell.column) }}
                          >
                            {flexRender(cell.column.columnDef.cell, cell.getContext())}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={columns.length} className="h-24 text-center">
                        No results.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
                {footerRow && (
                  <TableFooter>
                    <TableRow>
                      <TableHead colSpan={table.getAllFlatColumns().length}>
                        <div className="w-full border-t border-t-slate-200 p-2">{footerRow(table)}</div>
                      </TableHead>
                    </TableRow>
                  </TableFooter>
                )}
              </Table>
            </div>
            {showPagination && (
              <PaginationControlsWithServer
                legacyPage={legacyPage}
                className="mt-6"
                table={table}
                start={start}
                end={end}
                total={data.total}
              />
            )}
          </>
        ) : (
          <NoData
            icon={() =>
              hasQueryParams ? (
                <Binoculars className="h-12 w-12 text-slate-300" />
              ) : (
                <CalendarX className="h-12 w-12 text-slate-300" />
              )
            }
            title={
              hasQueryParams
                ? "No results found"
                : typeof emptyMessage === "string"
                  ? emptyMessage
                  : emptyMessage(table)
            }
            callback={() => {
              return hasQueryParams ? (
                <Button
                  variant={"outline"}
                  className="mt-4"
                  onClick={() => {
                    // rest all params
                    table.resetColumnFilters();
                    table.resetPagination();
                    router.push(pathname as Route);
                  }}
                >
                  Reset filters
                </Button>
              ) : (
                <></>
              );
            }}
          />
        )}
      </div>
    </React.Fragment>
  );
}
