"use client";

import type { BranchesListRes, IBranch } from "@/api/contracts/branch-contract";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { activeBranchIdAtom } from "@/components/app-sidebar/atoms";
import { Check, ChevronLeft, ChevronRight, RefreshCcw, Search } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { useParams } from "next/navigation";
import Link from "next/link";
import { useSetAtom } from "jotai";
import { useState, useMemo, useCallback } from "react";
import { debounce } from "lodash-es";

interface BranchSelectorProps {
  branches: BranchesListRes;
  branch?: IBranch;
}

export const BranchSelector = ({ branch, branches }: BranchSelectorProps) => {
  const locale = useLocale() as "en" | "ar";
  const t = useTranslations("common");
  const params = useParams();
  const branchId = Number(params.id);
  const setActiveBranchId = useSetAtom(activeBranchIdAtom);
  const [searchTerm, setSearchTerm] = useState("");

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setSearchTerm(term);
    }, 300),
    []
  );

  const handleSearchChange = (value: string) => {
    debouncedSearch(value);
  };

  // Filter branches based on search term
  const filteredBranches = useMemo(() => {
    if (!searchTerm.trim()) {
      return branches;
    }
    
    return branches.filter((branch) =>
      branch.name[locale]?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      branch.name.en?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      branch.name.ar?.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [branches, searchTerm, locale]);

  if (branches.length === 1) {
    return <div>{branch?.name?.[locale]}</div>;
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="flex w-full items-center justify-between gap-2">
          <div>{branch?.name?.[locale]}</div>
          <Button className="ml-auto" variant="secondary">
            <RefreshCcw className="mr-2 h-4 w-4" />
            {t("usermenu.switchBranch")}
          </Button>
        </div>
      </DialogTrigger>

      <DialogContent className="px-0 sm:max-w-[1024px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("usermenu.switchBranch2")}</DialogTitle>
          <DialogDescription>{t("usermenu.selectBranch")}</DialogDescription>
        </DialogHeader>
        
        {/* Search Input */}
        <div className=" py-2">
          <div className="relative">
            <Search className="absolute left-7 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search branches..."
              className="border-0 border-b border-gray-300 rounded-none px-6 pl-14 focus:border-blue-500 focus:ring-0"
              onChange={(e) => handleSearchChange(e.target.value)}
            />
          </div>
        </div>

        <div className="max-h-96 overflow-y-scroll pt-2">
          {filteredBranches.length === 0 ? (
            <div className="px-4 py-8 text-center text-gray-500">
              No branches found
            </div>
          ) : (
            filteredBranches.map((branch) => (
              <Link
                href={`/rental/branches/${branch.id}`}
                key={branch.id}
                onClick={() => setActiveBranchId(String(branch.id))}
              >
                <Button className="flex w-full justify-start text-left" variant="link" key={branch.id}>
                  {branch.id === branchId ? (
                    <div className="w-10">
                      <Check className="mr-2 h-4 w-4" />
                    </div>
                  ) : (
                    <div className="w-10" />
                  )}
                  <div>{branch.name[locale]}</div>
                  <div className="ml-auto">
                    {locale === "ar" ? <ChevronLeft className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                  </div>
                </Button>
              </Link>
            ))
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
