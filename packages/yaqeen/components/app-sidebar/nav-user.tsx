"use client";

import type { BranchesListRes } from "@/api/contracts/branch-contract";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from "@/components/ui/sidebar";
import { signOutAction } from "@/lib/actions";
import { setUserLocale } from "@/services/locale";
import { SignOut } from "@phosphor-icons/react/dist/ssr";
import { type User } from "next-auth";
import { useLocale, useTranslations } from "next-intl";
import { useParams } from "next/navigation";

import { useEffect } from "react";
import { initAll, setUserId } from "@amplitude/unified";
import * as amplitude from "@amplitude/unified";

import { BranchSelector } from "@/components/app-sidebar/branch-selector";

export function NavUser({ branches, user }: { branches: BranchesListRes; user: User | null }) {
  const locale = useLocale() as "en" | "ar";
  const { isMobile } = useSidebar();
  const params = useParams();
  const t = useTranslations("common");
  const branchId = () => {
    // debugger
    if (typeof window !== 'undefined') {
      const storedBranchId = (localStorage.getItem('yaqeen-active-branch'));
      if (storedBranchId) {
        return Number(JSON.parse(storedBranchId));
      }
    }
    return Number(params.id);
  };
  const branch = branches.find((branch) => (branch.id) === (branchId()));
  const { open } = useSidebar();

  const [name] = (user?.email?.split("@")[0]?.split(".") ?? []).map((n) => n.charAt(0).toUpperCase() + n.slice(1));

  const userInitials = user?.email
    ?.split("@")[0]
    ?.split(".")
    .map((name) => name?.[0]?.toUpperCase() ?? "")
    .join("");

  useEffect(() => {
    const effect = async () => {
      let apiKey = "4ae2d0893809ab8f5200554f75478ac7"; // staging
      if (new URL(window.location.href).origin === "https://yaqeen.lumirental.com") {
        apiKey = "8742bea80caca429aeb2d96656dcc1d2"; // production
      }
      const { init: engagementInit, plugin: engagementPlugin } = await import("@amplitude/engagement-browser");

      await initAll(apiKey, {
        sr: {
          sampleRate: 1,
        },
      });
      if (user?.email) {
        setUserId(user?.email);
      }
      amplitude.add(engagementPlugin());
      engagementInit(apiKey);
    };
    void effect();
  }, [user]);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton size="lg" aria-label="User menu">
              <div className="flex flex-1 gap-x-2 text-start text-sm leading-tight">
                <div className="flex h-10 w-10 items-center justify-center rounded bg-lime-400 text-center font-semibold">
                  <span className="ms-[-8px]">{userInitials}</span>
                </div>
                {open && (
                  <div className="grid flex-1 flex-col text-start text-sm leading-tight">
                    <span className="truncate text-sm font-medium">{name}</span>
                    <span className="truncate text-sm font-medium">{user?.email}</span>
                  </div>
                )}
              </div>
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width] min-w-80 rounded-lg"
            side={isMobile ? "bottom" : "right"}
            align="end"
            sideOffset={4}
          >
            <DropdownMenuLabel className="p-0 font-normal">
              <div className="gap-2 px-1 py-1.5 text-left text-sm">
                <div className="grid flex-1 truncate text-start text-sm leading-tight">
                  <span className="truncate font-semibold">{user?.name ?? "LUMI"}</span>
                  <span className="truncate text-xs">{user?.email}</span>
                </div>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onSelect={(e) => {
                e.preventDefault();
              }}
            >
              <BranchSelector branches={branches} branch={branch} />
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="!hover:bg-transparent flex justify-between text-sm text-slate-600">
              <span>{t("usermenu.changeLanguage")}</span>
              {locale === "en" && (
                <Button className="font-arabic text-blue-500" variant="link" onClick={() => setUserLocale("ar")}>
                  {t("usermenu.Arabic")}
                </Button>
              )}
              {locale === "ar" && (
                <Button className="text-blue-500" variant="link" onClick={() => setUserLocale("en")}>
                  {t("usermenu.English")}
                </Button>
              )}
            </DropdownMenuItem>

            <DropdownMenuSeparator />
            <DropdownMenuItem>
              <Button
                onClick={() => {
                  // resetActiveBranchId();
                  void signOutAction();
                }}
                variant="link"
                className="h-full w-full justify-start p-0 font-normal"
              >
                <SignOut className="mr-2 h-4 w-4" /> {t("usermenu.logout")}
              </Button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
