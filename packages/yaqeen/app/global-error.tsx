"use client";

import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";

export default function GlobalError({ error, reset }: {
  error: Error & { digest?: string }
  , reset: () => void
}) {
  const t = useTranslations("common");
  return (
    <html>
      <body className="flex h-screen w-full flex-col items-center justify-center">
        <h2>{t("errors.somethingWentWrong")}</h2>
        <div>{JSON.stringify(error?.message)}</div>
        <div>{JSON.stringify(error)}</div>
        <Button variant="default" onClick={() => reset()}>
          {t("tryAgain")}
        </Button>
      </body>
    </html>
  );
}
