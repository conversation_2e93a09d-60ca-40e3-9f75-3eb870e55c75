"use client";

import {
  closeAgreement,
  confirmVehicleReplacement,
  fetchInvoiceStatus,
  generateInvoice,
  initVehicleReplacement,
  retryAuthorization,
} from "@/lib/actions";
import { ContinueButton } from "@/components/ContinueButton";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/lib/hooks/use-toast";
import { cn } from "@/lib/utils";
import { XCircle, CaretLeft, CaretRight, Confetti, Printer } from "@phosphor-icons/react/dist/ssr";
import { useAtom } from "jotai";
import { useParams, usePathname, useRouter, useSearchParams } from "next/navigation";
import { useActionState, useMemo, useState, useEffect, startTransition } from "react";
import { type Route } from "next";
import { getNextTabUrl } from "./constants";
import { atomWithBookingNav } from "./atoms";
import { Dialog, DialogContent, DialogFooter, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { useTranslations } from "next-intl";
import type { ClosingPrice } from "@/api/contracts/booking/schema";
import { AlertTriangle, Loader2 } from "lucide-react";
import { type InvoiceSearchResponse, type InvoiceStatusResponse } from "@/api/contracts/booking/invoice-contract";
import {
  type Authorization,
  type CloseAgreementResponse,
  type InitVehicleReplacement,
} from "@/api/contracts/booking/booking-contract";
import { format } from "date-fns"; // Optional: Use date-fns for formatting
import { useProgressBar } from "@/components/progress-bar";
import { DownloadAgreement } from "../../bookings/_components/download-agreement";
import { AgreementInvoice } from "@/api/contracts/schema";

const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp * 1000); // Convert seconds to milliseconds
  return format(date, "dd/MM/yyyy - HH:mm:ss"); // Format using date-fns
};

export interface IConfirmReplacement {
  agreementNo: number;
  bookingNo: string;
  bookingId: number;
}

/**
 * Success modal displayed after creating an agreement
 */
const SuccessModal = ({
  isOpen,
  onOpenChange,
  bookingId,
}: {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  bookingId?: number;
}) => {
  const t = useTranslations("authorization");

  const progress = useProgressBar();
  const router = useRouter();
  const params = useParams();
  const branchId = Number(params.id);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent
        className="p-0 sm:w-[500px]"
        hideCross
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogHeader>
          <DialogTitle className="sr-only">Agreement updated Successfully</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col items-center justify-center gap-y-1 px-6 py-12 text-center">
          <Confetti className="h-20 w-full fill-lumi-700" />
          <p className="text-2xl font-bold text-lumi-800">Agreement updated Successfully</p>
          <p className="text-md font-normal">The new vehicle agreement details has been saved.</p>
        </div>
        <Separator />
        <DialogFooter className="gap-2 p-4">
          <Button
            className="inline-flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md border border-input bg-background px-4 py-2 text-sm ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            onClick={() => {
              progress?.start();
              startTransition(() => {
                router.push(`/rental/branches/${branchId}/bookings/${bookingId}`);
                progress?.done();
              });
            }}
          >
            {t("download.bookingDetails")}
          </Button>
          <Button
            className="flex h-10 w-full items-center justify-center whitespace-nowrap rounded-md bg-lumi-500 px-4 py-2 text-sm text-slate-900 ring-offset-background transition-colors hover:bg-lumi-600 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
            onClick={() => {
              progress?.start();
              startTransition(() => {
                router.push(`/rental/branches/${branchId}/bookings/ongoing`);
                progress?.done();
              });
            }}
          >
            {t("success.backToBookings")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export function ActionsBar({
  className,
  agreementNo,
  successCtaDisabled,
  isOtpAsked,
}: {
  className?: string;
  agreementNo: string;
  successCtaDisabled?: boolean;
  isOtpAsked?: boolean;
}) {
  const t = useTranslations("replaceVehicle");
  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const searchParams = useSearchParams();
  const params = useParams();
  const bookingId = Number(params.bookingId);
  const id = (params?.id as string) ?? "";
  const bookingNavAtom = useMemo(() => atomWithBookingNav(), [bookingId]);
  const [,] = useAtom(bookingNavAtom);
  // const [, updateNavItems] = useAtom(bookingNavAtom);
  const nextTabItemUrl = getNextTabUrl(pathname, agreementNo, id);

  const [isLoading, setIsLoading] = useState(false);
  const [updatedAgreement, setUpdatedAgreement] = useState<IConfirmReplacement | null>(null);

  const isAuthorizationPage = pathname.includes("/authorization");
  const isVehicleAssignmentPage = pathname.includes("/assign-vehicle");

  const [, formAction] = useActionState(async () => {
    const currentSearchParams = searchParams.toString();

    return router.push((nextTabItemUrl + "?" + currentSearchParams) as Route);
  }, null);

  const handleBack = () => {
    const currentSearchParams = searchParams.toString();
    router.back();
    if (currentSearchParams) {
      router.replace((window.location.pathname + "?" + currentSearchParams) as Route);
    }
  };

  const handleSaveExit = () => {
    // router.push(`/rental/branches/${id}/bookings`);
    window.location.href = `/rental/branches/${id}/bookings`;
  };

  const handleConfirmReplacement = async (isOTPValidated?: boolean) => {
    if (isOtpAsked && !isOTPValidated) {
      toast({
        title: t("toast.enterOtpTitle"),
        description: t("toast.enterOtpDesc"),
        variant: "warning",
      });
      return;
    }
    try {
      void setIsLoading(true); // Set loading state to true
      const vehicleStatus = searchParams.get("vehicleStatus");
      const response = await confirmVehicleReplacement(agreementNo, vehicleStatus ?? "");

      if (Number(response.status) === 200 || Number(response.status) === 201) {
        const result: IConfirmReplacement = response.body as IConfirmReplacement;
        void setUpdatedAgreement(result);
      } else {
        toast({
          title: "Error",
          description: "desc" in response.body ? response.body.desc.split(".").join(" ") : "An error occurred",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error replacement vehicle:", error);
      toast({
        title: "Error",
        description: "An error occurred while replacing the vehicle.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinue = async () => {
    if (isVehicleAssignmentPage) {
      try {
        const replacementVehiclePlateNo = searchParams.get("plateNo");
        const reasonType = searchParams.get("replaceReason");
        const vehicleStatus = searchParams.get("vehicleStatus");
        const agreementNo = typeof params.agreementNo === "string" ? params.agreementNo : "";
        if (!agreementNo) return;

        const response = await initVehicleReplacement(agreementNo ?? "", {
          replacementVehiclePlateNo: replacementVehiclePlateNo ?? "",
          replacement: {
            reasonType: reasonType ?? "",
          },
          replacedVehicleNextStatus: vehicleStatus ?? "",
        });
        if (Number(response.status) === 200 || Number(response.status) === 201) {
          const result = response.body as InitVehicleReplacement;
          const searchParams = new URLSearchParams(window.location.search);

          searchParams.set("vehicleId", result.id.toString());
          searchParams.set("plateNo", result.plateNo.toString());
          searchParams.set("vehicleGroupId", result.vehicleGroupId.toString());
          
          router.push(`${nextTabItemUrl}?${searchParams.toString()}` as Route);
        } else {
          toast({
            title: "Uh, something went wrong.",
            description: "desc" in response.body ? response.body.desc.split(".").join(" ") : "An error occurred",
            variant: "destructive",
          });

          const resp = response.body as { code: string; desc: string };
          if (resp.code === "CAS-7002") {
            // If the error is related to vehicle assignment, we can redirect to the assignment page
            const searchParams = new URLSearchParams(window.location.search);
            router.push(
              `/rental/branches/${id}/replace-vehicle/${agreementNo}/inspection-details?${searchParams.toString()}` as Route
            );
          }
        }
      } catch (error) {
        console.error("Error closing agreement:", error);
        toast({
          title: "Error",
          description: "An error occurred while assigning the vehicle.",
          variant: "destructive",
        });
      } finally {
      }
    }
  };

  return (
    <>
      <footer
        className={cn(
          "flex flex-wrap items-center rounded-lg border border-solid border-slate-200 bg-white text-sm font-medium leading-none text-slate-900 shadow",
          className
        )}
      >
        <div className="my-auto flex w-full  flex-1 shrink basis-0 flex-wrap items-center gap-4 self-stretch p-4">
          {!pathname.includes("/booking-details") && (
            <Button variant="outline" onClick={handleBack} className="flex items-center gap-2">
              <CaretLeft className="h-4 w-4" />
              {t("cta.back")}
            </Button>
          )}
          {/* <Button variant="outline" onClick={handleSaveExit} disabled={isLoading || successCtaDisabled}>
            {t("cta.exit")}
          </Button> */}
        </div>
        <form action={formAction} className="my-auto flex items-center gap-4 self-stretch p-4">
          {!isAuthorizationPage ? (
            <ContinueButton
              className="flex items-center"
              disabled={successCtaDisabled || isLoading}
              onClick={handleContinue}
            >
              <span>{t("cta.continue")} </span>
              <CaretRight className="mx-1 h-4 w-4" />
            </ContinueButton>
          ) : (
            <ContinueButton
              id="confirmReplacement"
              type="button"
              disabled={successCtaDisabled || isLoading}
              onClick={handleConfirmReplacement}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />} Save Agreement
            </ContinueButton>
          )}
        </form>
      </footer>

      <SuccessModal
        isOpen={!!updatedAgreement}
        onOpenChange={(isOpen) => {
          setUpdatedAgreement(null);
        }}
        bookingId={updatedAgreement?.bookingId}
      />
    </>
  );
}

export default ActionsBar;
