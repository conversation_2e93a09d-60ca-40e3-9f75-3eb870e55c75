"use client";

import { type TajeerAgreement } from "@/api/contracts/schema";
import ActionsBar from "../../actions-bar";
import Authorization from "./authorization";
import { ContextWrapper } from "../../_components/context-wrapper";
import { useState } from "react";

export function ClientWrapper({
  tajeerContracts,
  tammContracts,
  suggestAuth,
  tajeerLink,
  agreementNo,
  bookingType,
}: {
  tajeerContracts: TajeerAgreement[];
  tammContracts: TajeerAgreement[];
  suggestAuth: string;
  tajeerLink: string;
  agreementNo: string;
  bookingType: string;
}) {
  const [isOtpAsked, setOtpAsked] = useState(false);
  return (
    <ContextWrapper
      renderAction={({ isSuccessCtaDisabled }) => (
        <ActionsBar agreementNo={agreementNo ?? ""} className="w-full" successCtaDisabled={isSuccessCtaDisabled} isOtpAsked={isOtpAsked} />
      )}
    >
      <Authorization
        tajeerContracts={tajeerContracts}
        tammContracts={tammContracts}
        suggestAuth={suggestAuth}
        tajeerLink={tajeerLink}
        bookingType={bookingType}
        onOptShow={setOtpAsked}
      />
    </ContextWrapper>
  );
}
