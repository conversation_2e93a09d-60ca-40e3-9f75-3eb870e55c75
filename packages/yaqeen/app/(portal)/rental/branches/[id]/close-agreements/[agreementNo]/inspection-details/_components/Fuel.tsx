"use client";

import { useEffect, useState, useTransition } from "react";
import { Info, Loader2 } from "lucide-react";
import { type z } from "zod";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { PencilSimple } from "@phosphor-icons/react/dist/ssr";
import { useParams } from "next/navigation";
import { toast } from "@/lib/hooks/use-toast";
import { createInspection, revalidateClientPath, updateInspection, waveOffExtraCharge } from "@/lib/actions";
import { useTranslations } from "next-intl";
import clsx from "clsx";
import { FUEL_LEVELS, INSPECTION_TYPE, WAIVE_REASONS } from "../../constants";
import { type DriverExpenseSchema } from "@/api/contracts/booking/schema";
import { WaveOff } from "./Waveoff";
import { trackEvent } from "@/lib/utils";
import type { AgreementInvoice } from "@/api/contracts/schema";
import { useConsolidatedAgreementProperties } from "@/lib/hooks/useConsolidatedAgreementProperties";

type DriverExpense = z.infer<typeof DriverExpenseSchema>;

export function FuelDialog({
  checkinInspectionRefId,
  checkoutInspectionRefId,
  agreementVehicleId,
  checkinFuel,
  checkoutFuel,
  extraFuelCharges,
  isVehicleReplacement,
  bookingType,
  pricePerLiter,
  checkoutKm,
  checkinKm,
  agreement,
}: {
  checkinInspectionRefId: number | undefined;
  checkoutInspectionRefId: number;
  agreementVehicleId: number;
  checkinFuel: number | undefined;
  checkoutFuel: number;
  extraFuelCharges?: DriverExpense;
  isVehicleReplacement?: boolean;
  bookingType?: string;
  pricePerLiter: number;
  checkoutKm: number | undefined;
  checkinKm: number | undefined;
  agreement?: AgreementInvoice;
}) {
  const params = useParams();
  const agreementNo = params.agreementNo as string;
  const branchId = Number(params.id as string);
  const [isLoading, setIsLoading] = useState(false);

  const t = useTranslations("closeAgreement");
  const tErrors = useTranslations("errors");
  const consolidatedAgreementProperties = useConsolidatedAgreementProperties(agreement);

  const [pending, startTransition] = useTransition();
  const extraCharges = extraFuelCharges;
  const checkReason = WAIVE_REASONS.find((reason) => reason.value === extraCharges?.waiveOffReason);

  const [checkinFuelReading, setCheckinFuelReading] = useState(checkinFuel);
  const [checkoutFuelReading, setCheckoutFuelReading] = useState(checkoutFuel);
  const [waiveCharge, setWaiveCharge] = useState(extraCharges?.waiveOff);
  const [waiveReason, setWaiveReason] = useState(!checkReason ? "other" : extraCharges?.waiveOffReason);
  const [description, setDescription] = useState(!checkReason ? extraCharges?.waiveOffReason : "");
  const [hasApproval, setHasApproval] = useState(false);

  const [checkinChanged, setCheckinChanged] = useState(false);
  const [checkoutChanged, setCheckoutChanged] = useState(false);

  const [open, onOpenChange] = useState(false);

  const resetState = () => {
    setWaiveCharge(extraCharges?.waiveOff);
    setWaiveReason(!checkReason ? "other" : extraCharges?.waiveOffReason);
    setDescription(!checkReason ? extraCharges?.waiveOffReason : "");
  };

  useEffect(() => {
    return () => {
      setCheckinChanged(false);
      setCheckoutChanged(false);
    };
  }, []);

  const updateInspections = async () => {
    setIsLoading(true);
    const updatePromises = [];
    const payload = {
      agreementNo,
      agreementVehicleId,
      branchId,
    };

    if (checkoutChanged) {
      updatePromises.push(
        updateInspection({
          ...payload,
          inspectionType: INSPECTION_TYPE.CHECKOUT,
          odometerReading: checkoutKm,
          fuelLevel: checkoutFuelReading,
          inspectionId: checkoutInspectionRefId,
          ...(isVehicleReplacement && {
            replacement: isVehicleReplacement,
          }),
        })
      );
    }

    if (checkinChanged) {
      updatePromises.push(
        updateInspection({
          ...payload,
          inspectionType: INSPECTION_TYPE.CHECKIN,
          odometerReading: checkinKm,
          fuelLevel: checkinFuelReading,
          inspectionId: checkinInspectionRefId!,
          ...(isVehicleReplacement && {
            replacement: isVehicleReplacement,
          }),
        })
      );
    }

    // Run updates concurrently
    const responses = await Promise.all(updatePromises);

    let hasFailedUpdate = false;

    for (const response of responses) {
      if (response?.status !== 200) {
        hasFailedUpdate = true;
        toast({
          title: tErrors("title"),
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          description: tErrors(`${response?.body?.desc?.split(".")?.join(" ")}` as any),
          variant: "destructive",
        });
      }
    }

    return hasFailedUpdate;
  };

  const handleSaveChanges = async () => {
    if (!agreementVehicleId || !agreementNo) {
      toast({
        title: "Error",
        description: "Missing required information",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    try {
      try {
        trackEvent("Inspection Fuel Edited", {
          ...consolidatedAgreementProperties,
          inspection_app_checkin_fuel: checkinFuel,
          edited_checkin_fuel: checkinFuelReading,
          fuel_waived: waiveCharge ? "Yes" : "No",
          fuel_difference: (checkinFuelReading ?? 0) - (checkoutFuelReading ?? 0),
          fuel_waived_reason: waiveCharge ? (waiveReason === "other" ? description : waiveReason) : "",
        });
        const updatedObject = {
          agreementNo,
          agreementVehicleId,
          branchId,
        };

        let shouldUpdateCheckout = checkoutChanged;
        let shouldUpdateCheckin = checkinChanged;
        
        /**
         * Checkout initialization never been happen on yaqeen web side
        
        if (!checkoutInspectionRefId) {
          const checkoutResp = await createInspection({
            ...updatedObject,
            inspectionType: INSPECTION_TYPE.CHECKOUT,
            odometerReading: checkoutKm,
            fuelLevel: checkoutFuelReading,
            ...(isVehicleReplacement && { replacement: isVehicleReplacement }),
          });

          if (checkoutResp.status !== 200) {
            toast({
              title: tErrors("title"),
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              description: tErrors(`${checkoutResp?.body?.desc?.split(".")?.join(" ")}` as any),
              variant: "destructive",
            });
            return;
          }

          shouldUpdateCheckout = false;
          setCheckoutChanged(false);
        }
         */

        if (!checkinInspectionRefId) {
          const checkinResp = await createInspection({
            ...updatedObject,
            inspectionType: INSPECTION_TYPE.CHECKIN,
            odometerReading: checkinKm,
            fuelLevel: checkinFuelReading,
            ...(isVehicleReplacement && { replacement: isVehicleReplacement }),
          });

          if (checkinResp.status !== 200) {
            toast({
              title: tErrors("title"),
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              description: tErrors(`${checkinResp?.body?.desc?.split(".")?.join(" ")}` as any),
              variant: "destructive",
            });
            return;
          }

          shouldUpdateCheckin = false;
          setCheckinChanged(false);
        }

        if (shouldUpdateCheckin || shouldUpdateCheckout) {
          const hasFailedUpdate = await updateInspections();
          if (hasFailedUpdate) return;
        }

        if (bookingType === "B2B") {
          toast({
            title: t("inspection.toast.success.updated.title"),
            description: t("inspection.toast.success.updated.description"),
            variant: "success",
          });
          onOpenChange(false);
          return;
        }

        const shouldWaive = !!waiveCharge;
        const reason = waiveReason === "other" ? description : waiveReason;

        const waveoffResp = await waveOffExtraCharge(
          agreementNo,
          "EXTRA_FUEL_CHARGES",
          shouldWaive,
          branchId,
          !!isVehicleReplacement,
          shouldWaive ? reason : undefined
        );

        if (waveoffResp?.status !== 200) {
          toast({
            title: t("inspection.toast.error.waveoff.title"),
            description: t("inspection.toast.error.waveoff.description"),
            variant: "destructive",
          });
          return; // Stop further execution
        } else {
          toast({
            title: t("inspection.toast.success.updated.title"),
            description: t("inspection.toast.success.updated.description"),
            variant: "success",
          });
          onOpenChange(false);
        }
      } catch (e) {
        toast({
          title: tErrors("title"),
          description: tErrors("generic"),
          variant: "destructive",
        });
      }

      startTransition(async () => {
        // Refresh the page to show updated data
        await revalidateClientPath(
          `/rental/branches/${params.id as string}/close-agreements/${agreementNo}/inspection-details`
        );
      });
    } catch (error) {
      toast({
        title: tErrors("title"),
        description: tErrors("generic"),
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {/* Full screen loading  */}
      {pending && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
          <div className="loader animate-spin">
            <svg
              className="h-16 w-16 animate-spin text-gray-200"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
            >
              <circle cx="12" cy="12" r="10" strokeLinecap="round" />
            </svg>
          </div>
        </div>
      )}
      {/* Button to open the dialog */}
      <Button
        onClick={() => {
          onOpenChange(true);
          resetState();
        }}
        variant={"outline"}
        className="px-3"
      >
        <PencilSimple />
      </Button>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-md overflow-hidden p-0 sm:max-w-lg">
          <div className="flex items-center justify-between border-b p-4">
            <h2 className="text-lg font-bold">{t("Edit fuel level")}</h2>
          </div>

          <div className="p-4">
            <div className="mb-6 grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="checkout-fuel" className={clsx("mb-2 block text-start")}>
                  {t("Check-out fuel level")}
                </Label>
                <div className="relative">
                  <Select
                    onValueChange={(value) => {
                      setCheckoutChanged(Number(value) !== Number(checkoutFuelReading));
                      setCheckoutFuelReading(Number(value));
                    }}
                    defaultValue={checkoutFuelReading?.toString()}
                    disabled={!checkoutInspectionRefId ? true : false}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Fuel" />
                    </SelectTrigger>
                    <SelectContent>
                      {FUEL_LEVELS?.map((level) => (
                        <SelectItem key={level.value} value={level.value.toString()}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="checkin-fuel" className={clsx("mb-2 block text-start")}>
                  {t("Check-in fuel level")}
                </Label>
                <div className="relative">
                  <Select
                    onValueChange={(value) => {
                      setCheckinChanged(Number(value) !== Number(checkinFuelReading));
                      setCheckinFuelReading(Number(value));
                    }}
                    defaultValue={checkinFuelReading?.toString()}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Fuel" />
                    </SelectTrigger>
                    <SelectContent>
                      {FUEL_LEVELS?.map((level) => (
                        <SelectItem key={level.value} value={level.value.toString()}>
                          {level.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
            <div className="mb-6 rounded-md bg-slate-50 p-4">
              <div className="flex items-start justify-between">
                <div>
                  <div className="mb-1 text-gray-600">{t("Extra charge (Excl VAT)")}</div>
                  <div className="flex items-center gap-1">
                    <span className="font-semibold">
                      {t("SAR")} {extraFuelCharges?.totalSum ?? 0}
                    </span>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 cursor-pointer text-gray-500" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t("ratePerLevel", { rate: pricePerLiter })}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="waive-charge"
                    checked={waiveCharge}
                    disabled={bookingType === "B2B"}
                    onCheckedChange={(checked) => setWaiveCharge(checked as boolean)}
                  />
                  <Label htmlFor="waive-charge">{t("Waive extra charge")}</Label>
                </div>
              </div>
            </div>

            {waiveCharge && (
              <WaveOff
                waiveReason={waiveReason}
                setWaiveReason={setWaiveReason}
                description={description}
                setDescription={setDescription}
                hasApproval={hasApproval}
                setHasApproval={setHasApproval}
              />
            )}
          </div>

          <div className="flex justify-end gap-3 border-t p-4">
            <Button
              variant="outline"
              onClick={() => {
                onOpenChange(false);
                resetState();
              }}
              disabled={isLoading}
            >
              {t("Cancel")}
            </Button>
            <Button
              className="bg-[#a6d34e] text-black hover:bg-[#95c040]"
              onClick={handleSaveChanges}
              disabled={
                isLoading ||
                (waiveCharge && !hasApproval) ||
                checkinFuelReading === null ||
                checkinFuelReading === undefined
              }
            >
              {t("Save changes")}
              {isLoading && <Loader2 className="ml-2 h-4 w-4 animate-spin" />}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
