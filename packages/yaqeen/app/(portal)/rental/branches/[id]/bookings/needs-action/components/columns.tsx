"use client";

import { type Row, type ColumnDef } from "@tanstack/react-table";
import { type CompletedBooking } from "../../_components/types";
import { DataTableRowActions } from "@/components/ui/data-table/data-table-row-actions";
import { CaretUpDown, CheckCircle, XCircle } from "@phosphor-icons/react/dist/ssr";

import { amountFormatter, convertPlateToArabic, formattedPickupTime, getBadgeColor } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import TooltipComponent from "@/components/tooltip-component";
import { Badge } from "@/components/ui/badge";
import { AgreementButton } from "../../_components/AgreementButton";
import { useLocale, useTranslations } from "next-intl";
import { arSA, enUS } from "date-fns/locale";
import { VehicleModel } from "@/api/contracts/rental/availability-contract";

const LocalizeText = ({ message }: { message: string }) => {
  const t = useTranslations("bookings.columns");
  // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'message' implicitly has an 'any' type.
  return <div>{t(message)}</div>;
};

export const columns: ColumnDef<CompletedBooking>[] = [
  {
    id: "id",
    accessorKey: "id",
    enableHiding: true,
  },
  {
    id: "agreementNo",
    accessorKey: "agreementNo",
    header: () => <LocalizeText message="agreementNo" />,
    cell: ({ row }) => {
      return row.getValue("agreementNo");
    },
  },
  {
    id: "bookingNo",
    accessorKey: "bookingNo",
    header: () => <LocalizeText message="bookingNo" />,
    cell: ({ row }) => {
      return row.getValue<CompletedBooking["bookingNo"]>("bookingNo");
    },
  },
  {
    accessorKey: "dropOffDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="dropOffTime" />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => <FormattedDropoffTime row={row} />,
  },
  {
    accessorKey: "driver",
    header: () => <LocalizeText message="driver" />,
    cell: ({ row }) => {
      return (
        <span className="flex items-center gap-x-1">
          <span className=" text-blue-600">
            {row.getValue<CompletedBooking["driver"]>("driver")?.firstName}{" "}
            {row.getValue<CompletedBooking["driver"]>("driver")?.lastName}
          </span>
        </span>
      );
    },
  },
  {
    accessorKey: "preferredVehicleGroup",
    enableHiding: true,
  },
  {
    accessorKey: "assignedVehicle",
    header: () => <LocalizeText message="vehicle" />,
    cell: ({ row }) => {
      const assigned = row.getValue<CompletedBooking["assignedVehicle"]>("assignedVehicle");
      const preferred = row.getValue<CompletedBooking["preferredVehicleGroup"]>("preferredVehicleGroup");

      const vehicle = (assigned || preferred) as unknown as { model: VehicleModel; plateNo: string };
      const model = vehicle.model;

      const plateNo = vehicle?.plateNo || "";
      const [plateNumber = "", plateLetters = ""] = plateNo.split(" ");
      const arabicLetters = convertPlateToArabic(plateLetters.split("").reverse().join(" "));
      const arabicNumber = plateNumber.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[+d] ?? d);
      const arabicPlateNo = `${arabicLetters} ${arabicNumber}`;

      return (
        <div className="flex flex-col">
          <TranslatedText>
            {(_t, locale) => (
              <>
                {model && (
                  <span className="font-medium text-slate-900">
                    {model.make?.name?.[locale]} {model.name?.[locale]}
                  </span>
                )}
                <span className="font-normal text-slate-900">{locale === "ar" ? arabicPlateNo : plateNo}</span>
              </>
            )}
          </TranslatedText>
        </div>
      );
    },
  },
  {
    accessorKey: "paymentStatus",
    enableHiding: true,
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="total" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <p className=" flex w-full items-center gap-x-1">
          {amountFormatter(Number(row.getValue<CompletedBooking["totalPrice"]>("totalPrice")))}
          {row.getValue<CompletedBooking["paymentStatus"]>("paymentStatus")?.toLowerCase() === "paid" ? (
            <TooltipComponent content="Paid">
              <CheckCircle weight="fill" className=" size-4 fill-green-600" />
            </TooltipComponent>
          ) : row.getValue<CompletedBooking["paymentStatus"]>("paymentStatus")?.toLowerCase() === "pending" ? (
            <TooltipComponent content="Unpaid">
              <XCircle weight="fill" className="size-4 fill-slate-400" />
            </TooltipComponent>
          ) : (
            <></>
          )}
        </p>
      );
    },
  },
  // commenting out the due amount field as we don't get this field in the api response
  // {
  //   accessorKey: "dueAmount",
  //   header: ({ column }) => {
  //     return (
  //       <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
  //         <LocalizeText message="dues" />
  //         <CaretUpDown className="ml-2 h-4 w-4" />
  //       </Button>
  //     );
  //   },
  //   cell: ({ row }) => {
  //     const amount = Number(row.getValue<CompletedBooking["dueAmount"]>("dueAmount") ?? 0);
  //     return <p className="flex">{amount ? amountFormatter(amount) : "No dues"}</p>;
  //   },
  // },
  {
    accessorKey: "pendingAction",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <LocalizeText message="pendingAction" />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <TranslatedText>
          {(t) => <p className="flex">{t(row.getValue<CompletedBooking["pendingAction"]>("pendingAction"))}</p>}
        </TranslatedText>
      );
    },
  },
  {
    accessorKey: "status",
    header: () => <LocalizeText message="status" />,
    cell: ({ row }) => {
      const status = row.getValue<CompletedBooking["status"]>("status");
      return (
        <Badge
          variant="secondary"
          className={`rounded-full px-3 font-normal capitalize ${getBadgeColor(status ?? "UPCOMING")}`}
        >
          <TranslatedText>{(t) => t(status ?? "UPCOMING")}</TranslatedText>
        </Badge>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const status = row.getValue<CompletedBooking["status"]>("status");
      return (
        <DataTableRowActions row={row}>
          {status !== "COMPLETED" && (
            <AgreementButton buttonTitle="endAgreement" rowId={row.original.agreementNo} booking={row.original}>
              <LocalizeText message={"actions.endAgreement"} />
            </AgreementButton>
          )}
        </DataTableRowActions>
      );
    },
  },
];

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: "en" | "ar") => React.ReactNode;
}) => {
  const t = useTranslations("bookings.columns");
  const locale = useLocale() as "en" | "ar";
  return <>{children(t, locale)}</>;
};

const FormattedDropoffTime = ({ row }: { row: Row<CompletedBooking> }) => {
  const locale = useLocale();
  const nLocale = locale === "ar" ? arSA : enUS;
  const formatTime = formattedPickupTime(
    row.getValue<CompletedBooking["dropOffDateTime"]>("dropOffDateTime"),
    "",
    nLocale
  );
  return formatTime ? (
    <div className="flex flex-col items-start">
      <span>{formatTime.formattedString}</span>
      <span className={formatTime.colorClass}>{formatTime.displayText}</span>
    </div>
  ) : (
    "N/A"
  );
};
