"use client";

import { useL<PERSON>ale, useTranslations } from "next-intl";
import { type Row, type ColumnDef } from "@tanstack/react-table";
import { type AllBooking } from "./_components/types";
import { DataTableRowActions } from "@/components/ui/data-table/data-table-row-actions";
import { CaretUpDown, CheckCircle, XCircle } from "@phosphor-icons/react/dist/ssr";
import { amountFormatter, convertPlateToArabic, formattedPickupTime, getBadgeColor, toNormal } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import TooltipComponent from "@/components/tooltip-component";
import { Badge } from "@/components/ui/badge";
import { AgreementButton } from "./_components/AgreementButton";
import { getColor } from "./_components/constants";
import { arSA, enUS } from "date-fns/locale";

const Message = ({
  message,
}: {
  message: ReturnType<typeof useTranslations<"bookings">> extends (key: infer K) => unknown ? K : never;
}) => {
  const t = useTranslations("bookings");
  return <div>{t(message)}</div>;
};

export const columns: ColumnDef<AllBooking>[] = [
  {
    id: "id",
    accessorKey: "id",
    enableHiding: true,
  },
  {
    id: "agreementNo",
    accessorKey: "agreementNo",
    header: () => <Message message={"columns.agreementNo"} />,
    cell: ({ row }) => {
      return row.getValue("agreementNo") ?? "---";
    },
  },
  {
    id: "bookingNo",
    accessorKey: "bookingNo",
    header: () => {
      return <Message message={"columns.bookingNo"} />;
    },
    cell: ({ row }) => {
      return row.getValue<AllBooking["bookingNo"]>("bookingNo");
    },
  },
  {
    accessorKey: "bookingDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message message={"columns.bookingDate"} />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => <FormattedBookingTime row={row} />,
  },
  {
    accessorKey: "pickupDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message message={"columns.pickupTime"} />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => <FormattedPickupTime row={row} />,
  },
  {
    accessorKey: "dropOffDateTime",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message message={"columns.dropOffTime"} />
          <CaretUpDown className="ml-2 h-4 w-4 " />
        </Button>
      );
    },
    cell: ({ row }) => <FormattedDropoffTime row={row} />,
  },
  {
    accessorKey: "driver",
    header: () => {
      return <Message message={"columns.driver"} />;
    },
    cell: ({ row }) => {
      return (
        <span className="flex items-center gap-x-1">
          <span className=" text-blue-600">
            {row.getValue<AllBooking["driver"]>("driver")?.firstName}{" "}
            {row.getValue<AllBooking["driver"]>("driver")?.lastName}
          </span>
        </span>
      );
    },
  },
  {
    accessorKey: "preferredVehicleGroup",
    enableHiding: true,
  },
  {
    accessorKey: "assignedVehicle",
    header: () => {
      return <Message message={"columns.vehicle"} />;
    },
    cell: ({ row }) => {
      const assigned = row.getValue<AllBooking["assignedVehicle"]>("assignedVehicle");
      const preferred = row.getValue<AllBooking["preferredVehicleGroup"]>("preferredVehicleGroup");

      const vehicle = assigned || preferred;

      const plateNo = vehicle?.plateNo || "";
      const [plateNumber = "", plateLetters = ""] = plateNo.split(" ");
      const arabicLetters = convertPlateToArabic(plateLetters.split("").reverse().join(" "));
      const arabicNumber = plateNumber.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[+d] ?? d);
      const arabicPlateNo = `${arabicLetters} ${arabicNumber}`;

      return (
        <div className="flex flex-col">
          <TranslatedText>
            {(_t, locale) => (
              <>
                <span className="font-medium text-slate-900">
                  {vehicle?.model?.make?.name?.[locale]} {vehicle?.model?.name?.[locale]}
                </span>
                <span className="font-normal text-slate-900">{locale === "ar" ? arabicPlateNo : plateNo}</span>
              </>
            )}
          </TranslatedText>
        </div>
      );
    },
  },
  {
    accessorKey: "source",
    header: () => {
      return <Message message={"columns.source"} />;
    },
    cell: ({ row }) => {
      return row.getValue<AllBooking["source"]>("source") ?? <Message message={"columns.na"} />;
    },
  },
  {
    accessorKey: "status",
    header: () => {
      return <Message message={"columns.status"} />;
    },
    cell: ({ row }) => {
      const status = row.getValue<AllBooking["status"]>("status");
      return (
        <Badge
          variant="secondary"
          className={`rounded-full px-3 text-center font-normal capitalize ${getBadgeColor(status ?? "UPCOMING")}`}
        >
          <TranslatedText>{(t) => t(toNormal(status ?? "UPCOMING"))}</TranslatedText>
        </Badge>
      );
    },
  },
  {
    accessorKey: "paymentStatus",
    enableHiding: true,
  },
  {
    accessorKey: "totalPrice",
    header: ({ column }) => {
      return (
        <Button variant="ghost" onClick={() => column.toggleSorting(column.getIsSorted() === "asc")} className="p-0">
          <Message message={"columns.total"} />
          <CaretUpDown className="ml-2 h-4 w-4" />
        </Button>
      );
    },
    cell: ({ row }) => {
      return (
        <TranslatedText>
          {(t) => (
            <p className=" flex w-full items-center gap-x-1">
              {amountFormatter(Number(row.getValue<AllBooking["totalPrice"]>("totalPrice")))}
              {row.getValue<AllBooking["paymentStatus"]>("paymentStatus")?.toLowerCase() === "paid" ? (
                <TooltipComponent content={t("Paid")}>
                  <CheckCircle weight="fill" className=" size-4 fill-green-600" />
                </TooltipComponent>
              ) : (
                <TooltipComponent content={t("Unpaid")}>
                  <XCircle weight="fill" className="size-4 fill-slate-400" />
                </TooltipComponent>
              )}
            </p>
          )}
        </TranslatedText>
      );
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const status = row.getValue<AllBooking["status"]>("status");
      const showFullMenu = !["COMPLETED", "CANCELLED"].includes(status ?? "");
      const showMenu = !["CANCELLED"].includes(status ?? "");
      return (
        <DataTableRowActions row={row} showFullMenu={showFullMenu} showMenu={showMenu}
        pickupBranchId={row.original.pickupBranchId?.toString()}
        >
          {["LATE_RETURN", "ONGOING"].includes(status) ? (
            <AgreementButton buttonTitle={"endAgreement"} rowId={row.original?.agreementNo} booking={row.original}>
              <Message message={"actions.endAgreement"} />
            </AgreementButton>
          ) : !["COMPLETED", "CANCELLED"].includes(status) ? (
            <AgreementButton buttonTitle={"startAgreement"} rowId={row.getValue("id")} booking={row.original}>
              <Message message={"actions.startAgreement"} />
            </AgreementButton>
          ) : null}
        </DataTableRowActions>
      );
    },
  },
];

const TranslatedText = ({
  children,
}: {
  children: (t: ReturnType<typeof useTranslations>, locale: "en" | "ar") => React.ReactNode;
}) => {
  const t = useTranslations("bookings.columns");
  const locale = useLocale() as "en" | "ar";
  return <>{children(t, locale)}</>;
};

const FormattedBookingTime = ({ row }: { row: Row<AllBooking> }) => {
  const locale = useLocale() as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;
  const status = row.getValue<AllBooking["status"]>("status");
  const formatTime = formattedPickupTime(row.getValue<AllBooking["pickupDateTime"]>("pickupDateTime"), status, nLocale);
  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
    </div>
  );
};

const FormattedPickupTime = ({ row }: { row: Row<AllBooking> }) => {
  const locale = useLocale() as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;
  const status = row.getValue<AllBooking["status"]>("status");
  const formatTime = formattedPickupTime(row.getValue<AllBooking["pickupDateTime"]>("pickupDateTime"), status, nLocale);
  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
      {status === "UPCOMING" ? (
        <span className={`${getColor(formatTime.colorClass)} capitalize`}>{formatTime.displayText}</span>
      ) : (
        <></>
      )}
    </div>
  );
};

const FormattedDropoffTime = ({ row }: { row: Row<AllBooking> }) => {
  const locale = useLocale() as "en" | "ar";
  const nLocale = locale === "en" ? enUS : arSA;
  const status = row.getValue<AllBooking["status"]>("status");
  const formatTime = formattedPickupTime(
    row.getValue<AllBooking["dropOffDateTime"]>("dropOffDateTime"),
    status,
    nLocale
  );
  return (
    <div className="flex flex-col items-start">
      <span className="capitalize">{formatTime.formattedString}</span>
      {status === "ONGOING" ? <span className={getColor(formatTime.colorClass)}>{formatTime.displayText}</span> : <></>}
    </div>
  );
};
