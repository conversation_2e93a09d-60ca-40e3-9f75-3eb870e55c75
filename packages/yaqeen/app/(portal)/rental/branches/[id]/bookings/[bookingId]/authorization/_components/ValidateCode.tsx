"use client";
import { useTranslations } from "next-intl";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { initiateTajeer, initiateTamm, resendOtp, validateTajeer, validateTamm } from "@/lib/actions";
import type { ErrorSchema, TajeerAgreement } from "@/api/contracts/schema";
import { CheckCircle, XCircle } from "@phosphor-icons/react/dist/ssr";
import { Eye, EyeIcon, Loader2, SquareArrowOutUpRight } from "lucide-react";

import { useRef, useReducer, useState, useEffect } from "react";
import { useToast } from "@/lib/hooks/use-toast";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import type { ITajeerAgreement } from "@/api/contracts/tajeer-contract";
import { useQueryState } from "nuqs";
import Link from "next/link";
import type { Booking, CalculatedPrice, Pos } from "@/api/contracts/booking/schema";
import PaymentFlow from "../../payment/_components/PaymentFlow";
import { Table, TableBody, TableHeader } from "@/components/ui/table";
import { Calendar, CreditCard, Coins, User } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";

import { amountFormatter, toSaudiZoned, trackEvent } from "@/lib/utils";
import { format } from "date-fns";
import { type DepositWithdrawnDetail } from "@/api/contracts/schema";
import { Fragment } from "react";
import { Coin, CashRegister } from "@phosphor-icons/react";
import { useConsolidatedBookingProperties } from "@/lib/hooks/useConsolidatedBookingProperties";

const rootState = {
  loading: false,
  paymentModalOpen: false,
  manualCodePanel: false,
  showCodePanel: false,
  tajeerError: null as TajeerAgreement | null,
  inOTP: false,
  isOTPExpired: false,
  tammCodePanel: false,
  tammError: null as TajeerAgreement | null,
  isAuthorized: false,
  bookingNo: "",
  tajeerLink: "",
};

type State = typeof rootState;
type Action = {
  type: keyof State;
  value: boolean | ErrorSchema | null;
};

function reducer(state: State, action: Action) {
  return { ...state, [action.type]: action.value };
}

export default function ValidateCode({
  tajeerContracts,
  posResponseData,
  tajeerLink,
  bookingNo = "",
  disableSecurityDeposit = false,
  isB2b,
  calculatedPrice,
  authorizationStatus,
  displayOnly = false,
  suggestAuth,
  booking,
}: {
  tajeerLink: string;
  isB2b: boolean;
  posResponseData?: Pos;
  tajeerContracts: TajeerAgreement[];
  bookingNo: string;
  disableSecurityDeposit?: boolean;
  calculatedPrice?: CalculatedPrice | null;
  authorizationStatus?: number;
  displayOnly?: boolean;
  suggestAuth?: string;
  booking?: Booking;
}) {
  const [, setDriverAuthId] = useQueryState("driverAuthId", {
    shallow: false,
  });
  const [loading, setLoading] = useState(false);
  const [, setSuccesModal] = useQueryState("isSuccessModal", {
    shallow: false,
  });
  const getEventProperties = useConsolidatedBookingProperties(booking);
  const baseProperties = getEventProperties("authorization");

  const contract = tajeerContracts.find(
    (contract) => contract.status === "SUCCESS" && ["TAJEER", "TAMM"].includes(contract.type)
  );
  const securityDeposits: TajeerAgreement[] = tajeerContracts.filter(
    (contract) => contract.status === "SUCCESS" && contract.type === "SECURITY_DEPOSIT"
  );
  const isAuthorized = contract?.type === "TAJEER";
  const isTammAuthorized = contract?.type === "TAMM";

  const failedTajeerContracts = tajeerContracts.filter(
    (contract) => contract.status === "FAILED" && contract.type === "TAJEER"
  );

  const failedTammContracts: TajeerAgreement[] = tajeerContracts.filter(
    (contract) => contract.status === "FAILED" && contract.type === "TAMM"
  );

  console.log({ failedTammContracts });

  // This means only OTP is pending
  const inOTP = tajeerContracts.some(
    (contract) =>
      contract.status === "IN_PROGRESS" &&
      (contract.metadata.createStatus === "PENDING" || contract.metadata.createStatus === "FAILED")
  );
  const isOTPExpired = tajeerContracts.some(
    (contract) =>
      contract.status === "IN_PROGRESS" &&
      contract.metadata?.failureReason?.desc === "server.error.contract.otp.timeout.passed"
  );
  if (failedTajeerContracts.length > 0) {
    rootState.tajeerError = failedTajeerContracts[0] || null;
  }
  if (failedTammContracts.length > 0) {
    rootState.tammError = failedTammContracts[0] || null;
  }

  const [state, dispatch] = useReducer(reducer, {
    ...rootState,
    inOTP,
    isOTPExpired,
    isAuthorized,
    bookingNo,
    tajeerLink,
  });
  const [, setAuthorizationTries] = useQueryState("authorization");

  useEffect(() => {
    if (isAuthorized || isTammAuthorized || (authorizationStatus !== undefined && authorizationStatus > 0)) {
      void setAuthorizationTries("1");
      if (isTammAuthorized) {
        dispatch({ type: "tammError", value: null });
        dispatch({ type: "tammCodePanel", value: false });
        dispatch({ type: "tajeerError", value: null });
        dispatch({ type: "loading", value: false });
      }
    }
  }, [isAuthorized, isTammAuthorized, authorizationStatus]);

  const t = useTranslations("authorization");
  if (displayOnly && tajeerContracts.length === 0) {
    return null;
  }
  console.log({ suggestAuth });
  return (
    <>
      <Card className="col-span-8 h-fit w-full overflow-hidden !p-0">
        <CardHeader className="flex flex-row items-center justify-between border-b px-4 py-6">
          <CardTitle>{t("title")}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6 p-0">
          {(suggestAuth === "TAJEER" || displayOnly) && (
            <div className="space-y-1.5 px-4 pt-6">
              <div className="flex items-center justify-between">
                <div>
                  {!isB2b && (
                    <>
                      <div className="mb-2 flex gap-2">
                        <h3 className="text-lg font-semibold">{t("tajeer.title")}</h3>
                        {isAuthorized && (
                          <span className="inline-flex items-center gap-1 rounded-full bg-lumi-200 px-2 py-1 text-sm">
                            <CheckCircle weight="fill" color="#5B8902" className="h-3.5 w-3.5" />
                            <span className="text-lumi-700">{t("tajeer.authorized")}</span>
                          </span>
                        )}
                        {(state.tajeerError || failedTajeerContracts.length > 0) && !isAuthorized && (
                          <span className="inline-flex items-center gap-1 rounded-full bg-red-100 px-2 py-1 text-sm">
                            <XCircle weight="fill" color="#DC2626" className="h-3.5 w-3.5" />
                            <span className="text-red-600">{t("tajeer.failed")}</span>
                          </span>
                        )}
                      </div>
                      <p className="break-all text-sm text-gray-500">{t("tajeer.description")}</p>
                    </>
                  )}
                </div>
                {state.tajeerError && !displayOnly ? (
                  <TajeerError setDriverAuthId={setDriverAuthId} state={state} dispatch={dispatch} />
                ) : displayOnly && !isAuthorized && !isB2b ? (
                  <div>{t("tajeer.Tajeer skipped")}</div>
                ) : (
                  !isTammAuthorized &&
                  !displayOnly && (
                    <>
                      <AuthorizeButton
                        setDriverAuthId={setDriverAuthId}
                        state={state}
                        dispatch={dispatch}
                        eventProperties={baseProperties}
                      />
                    </>
                  )
                )}
              </div>

              {state.showCodePanel && (
                <OTPCode
                  state={state}
                  dispatch={dispatch}
                  setSuccesModal={setSuccesModal}
                  eventProperties={baseProperties}
                />
              )}
              {isOTPExpired && <ResendOTP state={state} dispatch={dispatch} />}
              {state.manualCodePanel && (
                <ManualCode
                  state={state}
                  dispatch={dispatch}
                  setSuccesModal={setSuccesModal}
                  eventProperties={baseProperties}
                />
              )}

              {isAuthorized && (
                <div className="mt-4 rounded-lg bg-gray-50 p-6">
                  <div className="mb-4 flex items-center justify-between">
                    <div>
                      <h4 className="text-base font-medium text-gray-700">{t("tajeer.contractNo")}</h4>
                      <p className="break-all font-mono text-gray-900">{contract?.externalAuthorizationNumber}</p>
                    </div>
                    <Link
                      target="_blank"
                      href={`/contract/pdf?referenceNumber=${bookingNo}`}
                      prefetch={false}
                      className="gap-2"
                    >
                      <Button variant="outline" className="flex items-center gap-1">
                        <Eye className="h-4 w-4"  />
                        {t("tajeer.viewContract")}
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
              {state.tajeerError || failedTajeerContracts.length > 0 ? (
                <div>
                  {failedTajeerContracts?.map((contract) => (
                    <div key={contract.id} className="rounded-lg bg-gray-50 p-4">
                      <div className="text-base font-medium text-gray-700">Reason</div>
                      <div className="max-w-3xl">{contract?.metadata?.failureReason?.desc?.split(".")?.join(" ")}</div>
                    </div>
                  ))}
                </div>
              ) : null}
              {isTammAuthorized && (
                <div>
                  <div>
                    <h3 className="flex gap-2 text-lg font-semibold">
                      <div>{t("tamm.title")}</div>
                      <span className="inline-flex items-center gap-1 rounded-full bg-lumi-200 px-2 text-sm">
                        <CheckCircle weight="fill" color="#5B8902" className="h-3.5 w-3.5" />
                        <span className="text-lumi-700">{t("tajeer.authorized")}</span>
                      </span>
                    </h3>
                    <p className="break-all text-sm text-gray-500">{t("tamm.description")}</p>
                  </div>
                  <div className="mt-4 rounded-lg bg-gray-50 p-6">
                    <div className="mb-4 flex items-center justify-between">
                      <div>
                        <p className="text-slate-500">{t("tamm.authNumber")}</p>
                        <p className="break-all font-mono text-gray-900">
                          {contract?.externalAuthorizationNumber ?? "xxxxxxxxxxxxx"}
                        </p>
                      </div>
                      <div>
                        <p className="text-slate-900">{t("tamm.authOn")}</p>
                        <p className="break-all font-mono text-gray-900">
                          {/* {new Date((contract?.createdOn ?? 0) * 1000).toLocaleString()} */}
                          {contract?.createdOn
                            ? format(toSaudiZoned(contract?.createdOn * 1000), "dd/MM/yyyy - HH:mm:ss")
                            : "N/A"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
          {((state.tajeerError || suggestAuth === "TAMM") && !isTammAuthorized) ||
          (!displayOnly && suggestAuth === "TAMM" && isTammAuthorized) ? (
            <>
              <Separator className="p-0" />
              {isTammAuthorized ? (
                <div className="mx-4">
                  <div>
                    <h3 className="flex gap-2 text-lg font-semibold">
                      <div>{t("tamm.title")}</div>
                      <span className="inline-flex items-center gap-1 rounded-full bg-lumi-200 px-2 text-sm">
                        <CheckCircle weight="fill" color="#5B8902" className="h-3.5 w-3.5" />
                        <span className="text-lumi-700">{t("tajeer.authorized")}</span>
                      </span>
                    </h3>
                    <p className="break-all text-sm text-gray-500">{t("tamm.description")}</p>
                  </div>
                  <div className="mt-4 rounded-lg bg-gray-50 p-6">
                    <div className="mb-4 flex items-center justify-between">
                      <div>
                        <p className="text-slate-500">{t("tamm.authNumber")}</p>
                        <p className="break-all font-mono text-gray-900">
                          {contract?.externalAuthorizationNumber ?? "xxxxxxxxxxxxx"}
                        </p>
                      </div>
                      <div>
                        <p className="text-slate-900">{t("tamm.authOn")}</p>
                        <p className="break-all font-mono text-gray-900">
                          {/* {new Date((contract?.createdOn ?? 0) * 1000).toLocaleString()} */}
                          {contract?.createdOn
                            ? format(toSaudiZoned(contract?.createdOn * 1000), "dd/MM/yyyy - HH:mm:ss")
                            : "N/A"}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <TammFlow
                  setDriverAuthId={setDriverAuthId}
                  state={state}
                  displayOnly={displayOnly}
                  dispatch={dispatch}
                  isTammAuthorized={isTammAuthorized}
                  failedTammContracts={failedTammContracts}
                  eventProperties={baseProperties}
                />
              )}
            </>
          ) : (
            <></>
          )}

          {state.tammCodePanel && <TammOTPCode state={state} dispatch={dispatch} eventProperties={baseProperties} />}
          <Separator className="p-0" />
          {!isB2b && (
            <SecurityDepositButton
              disableSecurityDeposit={disableSecurityDeposit}
              securityDeposits={securityDeposits}
              dispatch={dispatch}
            />
          )}
        </CardContent>
      </Card>

      {posResponseData ? (
        <PaymentFlow
          loading={loading}
          setLoading={setLoading}
          isValidateCodePage={true}
          initiatedFor="SECURITY_DEPOSIT_AUTHORIZATION"
          posResponse={posResponseData}
          paymentModalOpen={state.paymentModalOpen}
          calculatedPrice={calculatedPrice}
          bookingNo={bookingNo}
          setPaymentModalOpen={(value) => dispatch({ type: "paymentModalOpen", value })}
        />
      ) : null}
    </>
  );
}

const TammFlow = ({
  state,
  dispatch,
  setDriverAuthId,
  failedTammContracts,
  isTammAuthorized,
  displayOnly,
  eventProperties,
}: {
  setDriverAuthId: (val: string) => void;
  state: State;
  dispatch: (value: Action) => void;
  isTammAuthorized: boolean;
  failedTammContracts: TajeerAgreement[];
  displayOnly: boolean;
  eventProperties: Record<string, string | number | boolean | null>;
}) => {
  const t = useTranslations("authorization");
  return (
    <div className="space-y-1.5 px-4">
      <div className="flex flex-col justify-between">
        <div className="flex justify-between">
          <section>
            <h3 className="flex gap-2 text-lg font-semibold">
              <div>{t("tamm.title")}</div>
              {state.tammError ? (
                <span className="inline-flex items-center gap-1 rounded-full bg-red-100 px-2 py-1 text-sm">
                  <XCircle weight="fill" color="#DC2626" className="h-3.5 w-3.5" />
                  <span className="text-red-600">{t("tajeer.failed")}</span>
                </span>
              ) : isTammAuthorized ? (
                <span className="inline-flex items-center gap-1 rounded-full bg-lumi-200 px-2 text-sm">
                  <CheckCircle weight="fill" color="#5B8902" className="h-3.5 w-3.5" />
                  <span className="text-lumi-700">{t("tajeer.authorized")}</span>
                </span>
              ) : (
                <></>
              )}
            </h3>
            <p className="break-all text-sm text-gray-500">{t("tamm.description")}</p>
          </section>
          {!displayOnly ? (
            <>
              {state.tammError ? (
                <TammError
                  setDriverAuthId={setDriverAuthId}
                  state={state}
                  dispatch={dispatch}
                  displayOnly={displayOnly}
                />
              ) : (
                <TammAuthorizeButton
                  setDriverAuthId={setDriverAuthId}
                  state={state}
                  dispatch={dispatch}
                  eventProperties={eventProperties}
                />
              )}
            </>
          ) : (
            <></>
          )}
        </div>
        {state.tammError && (
          <div>
            {/* <TammError setDriverAuthId={setDriverAuthId} state={state} dispatch={dispatch} /> */}
            {failedTammContracts.map((contract) => (
              <div key={contract.id} className="rounded-lg bg-gray-50 p-4">
                <div className="text-base font-medium text-gray-700">Reason</div>
                <div className="max-w-3xl">{contract?.metadata?.failureReason?.desc?.split(".")?.join(" ")}</div>
              </div>
            ))}
          </div>
        )}
        {/* // ) : (
        //   <>
        //     <TammAuthorizeButton setDriverAuthId={setDriverAuthId} state={state} dispatch={dispatch} />
        //   </>
        // )} */}
      </div>
    </div>
  );
};

const WithdrawnFromDeposit = ({ securityDeposit }: { securityDeposit: TajeerAgreement }) => {
  const t = useTranslations("authorization");
  const withdrawnDetails = securityDeposit.metadata.depositWithdrawnDetails;

  if (withdrawnDetails.length === 0) return null;

  return (
    <>
      <Separator />
      <div className="p-4">
        <h4 className="mb-4 text-lg font-semibold">{t("securityDeposit.withdrawnFromDeposit")}</h4>
        <div className="rounded-sm bg-slate-50 p-4">
          <Table className="w-full border-separate border-spacing-2">
            <TableHeader className="text-slate-500">
              <tr>
                <td>{t("securityDeposit.withdrawnAmount")}</td>
                <td>{t("securityDeposit.withdrawnOn")}</td>
                <td></td>
              </tr>
            </TableHeader>
            <TableBody>
              {withdrawnDetails.map((detail: DepositWithdrawnDetail, index) => (
                <Fragment key={detail.approvalCode + index}>
                  <tr>
                    <td>
                      {t("securityDeposit.SAR")} {amountFormatter(detail.withdrawnAmount)}
                    </td>
                    <td>{format(new Date(detail.withdrawnDate * 1000), "dd/MM/yyyy - hh:mm:ss a")}</td>
                    <td className="ml-auto">
                      <Sheet>
                        <SheetTrigger className="w-full">
                          <Button className="ml-auto flex gap-2" variant="outline">
                            <EyeIcon strokeWidth={1} size="small" />
                            {t("securityDeposit.View Details")}
                          </Button>
                        </SheetTrigger>
                        <SheetContent>
                          <SheetHeader>
                            <SheetTitle>{t("securityDeposit.Deposit Details")}</SheetTitle>
                            <SheetDescription>
                              <div className="space-y-4">
                                <div className="space-y-4">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                      <Coin size={24} className="shrink-0 text-slate-900" />
                                      <span className="text-base font-medium text-slate-900">
                                        {t("securityDeposit.SAR")} {amountFormatter(detail.withdrawnAmount)}
                                      </span>
                                    </div>
                                    <Badge className="bg-lumi-200 px-3 py-2 text-xs font-medium text-slate-900 hover:bg-lumi-400">
                                      {t("securityDeposit.Success")}
                                    </Badge>
                                  </div>

                                  <div className="flex items-center gap-2">
                                    <CreditCard size={24} className="shrink-0 text-slate-900" />
                                    <span className="text-base font-medium text-slate-900">
                                      {t("securityDeposit.Credit card deposit")}
                                    </span>
                                  </div>

                                  <div className="flex items-center gap-2">
                                    <CashRegister size={24} className="shrink-0 text-slate-900" />
                                    <span className="text-base font-medium text-slate-900">
                                      {t("securityDeposit.Collected By")} {detail.collectedBy}
                                    </span>
                                  </div>

                                  <div className="flex items-center gap-2">
                                    <Calendar size={24} className="shrink-0 text-slate-900" />
                                    <span className="text-base font-medium text-slate-900">
                                      {format(new Date(detail.withdrawnDate * 1000), "dd/MM/yyyy - hh:mm:ss a")}
                                    </span>
                                  </div>
                                </div>

                                <Separator />

                                <div className="space-y-4">
                                  <div className="space-y-2">
                                    <p className="text-sm font-medium text-slate-500">
                                      {t("securityDeposit.Receipt number")}
                                    </p>
                                    <p className="font-medium text-slate-900">XXXXXXXXX</p>
                                  </div>

                                  <Separator />

                                  <div className="space-y-2">
                                    <p className="text-sm font-medium text-slate-500">
                                      {t("securityDeposit.Last 4 digits")}
                                    </p>
                                    <p className="font-medium text-slate-900">{detail.cardLast4Digit}</p>
                                  </div>

                                  <Separator />

                                  <div className="space-y-2">
                                    <p className="text-sm font-medium text-slate-500">
                                      {t("securityDeposit.POS machine no")}
                                    </p>
                                    <p className="font-medium text-slate-900">
                                      {t("securityDeposit.POS")} {detail.posMachine}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            </SheetDescription>
                          </SheetHeader>
                        </SheetContent>
                      </Sheet>
                    </td>
                  </tr>
                </Fragment>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  );
};

const SecurityDepositButton = ({
  disableSecurityDeposit,
  securityDeposits,
  dispatch,
}: {
  disableSecurityDeposit: boolean;
  securityDeposits: TajeerAgreement[];
  dispatch: (value: Action) => void;
}) => {
  const t = useTranslations("authorization");
  const securityDepositCollected = securityDeposits.length > 0;
  const securityDeposit = securityDeposits.length > 0 ? securityDeposits[0]! : null;
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  return (
    <>
      <div className="space-y-1.5 px-4 pb-6">
        <div className="flex w-full items-center justify-between">
          <div className="flex-grow">
            <div className="flex gap-2 text-lg font-semibold">
              {disableSecurityDeposit ? (
                <div className="mb-1">{t("securityDeposit.deposit")}</div>
              ) : (
                <div className="mb-1">{t("securityDeposit.collectDeposit")}</div>
              )}
              {securityDepositCollected && (
                <span className="inline-flex items-center gap-1 rounded-full bg-lumi-200 px-2 text-sm">
                  <CheckCircle weight="fill" color="#5B8902" className="h-3.5 w-3.5" />
                  <span className="text-lumi-700">{t("tajeer.authorized")}</span>
                </span>
              )}
            </div>
            <p className="mb-2 break-all text-sm text-gray-500">{t("securityDeposit.description")}</p>
            {securityDepositCollected && (
              <div className="w-full rounded-sm bg-slate-50 p-4">
                <Table>
                  <TableHeader className="text-slate-500">
                    <tr>
                      <td>{t("securityDeposit.Amount")}</td>
                      <td>{t("securityDeposit.Authorized On")}</td>
                      <td></td>
                    </tr>
                  </TableHeader>
                  <TableBody>
                    <tr>
                      <td>
                        {t("securityDeposit.SAR")} {securityDeposit!.metadata.depositAmount}
                      </td>
                      <td>{new Date(securityDeposit!.createdOn * 1000).toLocaleString()}</td>
                      <td className="ml-auto">
                        <div>
                          <Sheet>
                            <SheetTrigger className="w-full">
                              <Button className="ml-auto flex gap-2" variant="outline">
                                <EyeIcon strokeWidth={1} size="small" />
                                {t("securityDeposit.View Details")}
                              </Button>
                            </SheetTrigger>
                            <SheetContent>
                              <SheetHeader>
                                <SheetTitle>{t("securityDeposit.Deposit Details")}</SheetTitle>
                                <SheetDescription>
                                  <div className="space-y-4 p-6">
                                    {/* Transaction details */}
                                    <div className="space-y-4">
                                      <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                          <Coins className="h-5 w-5 text-gray-700" />
                                          <span className="text-lg font-semibold">
                                            {t("securityDeposit.SAR")} {securityDeposit!.metadata.depositAmount}
                                          </span>
                                        </div>
                                        <Badge className="bg-[#e4f8d3] text-[#5a9c42] hover:bg-[#d8f0c7] hover:text-[#5a9c42]">
                                          {t("securityDeposit.Success")}
                                        </Badge>
                                      </div>

                                      <div className="flex items-center gap-3">
                                        <CreditCard className="h-5 w-5 text-gray-700" />
                                        <span className="text-gray-900">
                                          {t("securityDeposit.Credit card deposit")}
                                        </span>
                                      </div>

                                      <div className="flex items-center gap-3">
                                        <User className="h-5 w-5 text-gray-700" />
                                        <span className="text-gray-900">
                                          {t("securityDeposit.Collected By")} {securityDeposit!.createdBy}
                                        </span>
                                      </div>

                                      <div className="flex items-center gap-3">
                                        <Calendar className="h-5 w-5 text-gray-700" />
                                        <span className="text-gray-900">
                                          {new Date(securityDeposit!.createdOn * 1000).toLocaleString()}
                                        </span>
                                      </div>
                                    </div>

                                    <Separator />

                                    {/* Receipt details */}
                                    <div className="space-y-4">
                                      <div className="space-y-1">
                                        <p className="break-all text-sm text-gray-500">
                                          {t("securityDeposit.Receipt number")}
                                        </p>
                                        <p className="font-medium">XXXXXXXXX</p>
                                      </div>

                                      <Separator />

                                      <div className="space-y-1">
                                        <p className="break-all text-sm text-gray-500">
                                          {t("securityDeposit.Last 4 digits")}
                                        </p>
                                        <p className="break-all font-medium">
                                          {securityDeposit!.metadata.cardLast4Digit}
                                        </p>
                                      </div>

                                      <Separator />

                                      <div className="space-y-1">
                                        <p className="break-all text-sm text-gray-500">
                                          {t("securityDeposit.POS machine no")}
                                        </p>
                                        <p className="break-all font-medium">
                                          {t("securityDeposit.POS")} {securityDeposit!.metadata.posMachine}
                                        </p>
                                      </div>
                                    </div>
                                  </div>
                                </SheetDescription>
                              </SheetHeader>
                            </SheetContent>
                          </Sheet>
                        </div>
                      </td>
                    </tr>
                  </TableBody>
                </Table>
              </div>
            )}
          </div>
          {securityDeposits.length === 0 && !disableSecurityDeposit ? (
            <Button
              variant="outline"
              onClick={() => {
                dispatch({ type: "paymentModalOpen", value: true });
                void setAuthorizationMode("Security Deposit");
              }}
            >
              {t("securityDeposit.collectDeposit")}
            </Button>
          ) : null}
        </div>
      </div>
      {securityDeposit && <WithdrawnFromDeposit securityDeposit={securityDeposit} />}
    </>
  );
};

const TammError = ({
  state,
  dispatch,
  setDriverAuthId,
  displayOnly,
}: {
  setDriverAuthId: (value: string) => void;
  state: State;
  dispatch: (value: Action) => void;
  displayOnly?: boolean;
}) => {
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  if (displayOnly) return <></>;

  return (
    <div className="flex gap-2">
      <Button
        disabled={state.loading}
        variant="outline"
        onClick={async () => {
          if (state.bookingNo) {
            dispatch({ type: "loading", value: true });
            const tajeerResponse = await initiateTamm(state.bookingNo);
            if (tajeerResponse.status === 200) {
              if ("id" in tajeerResponse.body) {
                void setDriverAuthId(tajeerResponse.body.id.toLocaleString());
              }
              dispatch({ type: "showCodePanel", value: true });
            } else {
              dispatch({ type: "tammError", value: tajeerResponse.body as ErrorSchema });
              toast({
                title: t("errors.failedToAuthorizeTamm"),
                description: (state.tammError as unknown as ErrorSchema)?.desc?.split(".")?.join(" "),
                variant: "destructive",
              });
            }
            void setAuthorizationMode("Tamm");
            dispatch({ type: "loading", value: false });
          }
        }}
      >
        {state.loading ? <LoadingSpinner /> : t("tajeer.tryAgain")}
      </Button>
    </div>
  );
};

const TajeerError = ({
  state,
  dispatch,
  setDriverAuthId,
}: {
  setDriverAuthId: (value: string) => void;
  state: State;
  dispatch: (value: Action) => void;
}) => {
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  return (
    <div className="flex gap-2">
      <Button
        disabled={state.loading}
        onClick={() => {
          dispatch({ type: "manualCodePanel", value: true });
        }}
        variant="outline"
      >
        {state.loading ? <LoadingSpinner /> : t("tajeer.tryManually")}
      </Button>
      <Button
        disabled={state.loading}
        variant="outline"
        onClick={async () => {
          if (state.bookingNo) {
            dispatch({ type: "loading", value: true });
            const tajeerResponse = await initiateTajeer(state.bookingNo);
            if (tajeerResponse.status === 200) {
              void setDriverAuthId(tajeerResponse.body.id.toLocaleString());
              dispatch({ type: "showCodePanel", value: true });
            } else {
              dispatch({ type: "tajeerError", value: tajeerResponse.body as ErrorSchema });
              toast({
                title: t("errors.failedToAuthorize"),
                description: (state.tajeerError as unknown as ErrorSchema)?.desc?.split(".")?.join(" "),
                variant: "destructive",
              });
            }
            void setAuthorizationMode("Tamm");
            dispatch({ type: "loading", value: false });
          }
        }}
      >
        {state.loading ? <LoadingSpinner /> : t("tajeer.tryAgain")}
      </Button>
    </div>
  );
};

const AuthorizeButton = ({
  state,
  dispatch,
  setDriverAuthId,
  eventProperties,
}: {
  state: State;
  dispatch: (value: Action) => void;
  setDriverAuthId: (value: string) => void;
  booking?: Booking;
  eventProperties: Record<string, string | number | boolean | null>;
}) => {
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const { isAuthorized, loading, bookingNo } = state;
  const [authorizationTries, setAuthorizationTries] = useQueryState("authorization");
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  useEffect(() => {
    if (isAuthorized) {
      void setAuthorizationTries("1");
    }
  }, [isAuthorized]);

  return (
    <Button
      variant="outline"
      disabled={isAuthorized || loading}
      onClick={async () => {
        if (bookingNo) {
          dispatch({ type: "loading", value: true });
          try {
            const tajeerResponse = await initiateTajeer(bookingNo);
            if (Number(authorizationTries) < 1) {
              void setAuthorizationTries("1");
            }
            if (tajeerResponse.status === 200) {
              void setDriverAuthId(tajeerResponse.body.id.toLocaleString());
              dispatch({ type: "showCodePanel", value: true });
              dispatch({ type: "tajeerError", value: null });
              trackEvent("Tajeer Authorization Attempted", {
                ...eventProperties,
                attempt_count: Number(authorizationTries),
                tajeer_error: null,
                validation_error: null,
              });
            } else {
              dispatch({ type: "tajeerError", value: tajeerResponse.body as unknown as ErrorSchema });
              trackEvent("Tajeer Authorization Attempted", {
                ...eventProperties,
                attempt_count: Number(authorizationTries),
                tajeer_error: tajeerResponse?.body?.desc?.split(".")?.join(" "),
                validation_error: null,
              });
              if (tajeerResponse?.body) {
                toast({
                  title: t("tajeer.failedTitle"),
                  description: tajeerResponse?.body?.desc?.split(".")?.join(" "),
                  variant: "destructive",
                });
              }
            }
          } catch (e) {
            dispatch({
              type: "tajeerError",
              value: {
                code: "FAILED",
                reqId: "",
                desc: (e as Error)?.message,
              },
            });
            trackEvent("Tajeer Authorization Attempted", {
              ...eventProperties,
              attempt_count: Number(authorizationTries),
              tajeer_error: (e as Error)?.message,
              validation_error: null,
            });
            toast({
              title: t("tajeer.failedTitle"),
              description: (e as Error)?.message,
              variant: "destructive",
            });
          }
          void setAuthorizationMode("Tajeer");
          dispatch({ type: "loading", value: false });
        }
      }}
    >
      {loading ? <LoadingSpinner /> : t("tajeer.authorizeCustomer")}
    </Button>
  );
};

const TammAuthorizeButton = ({
  state,
  dispatch,
  setDriverAuthId,
  eventProperties,
}: {
  state: State;
  dispatch: (value: Action) => void;
  setDriverAuthId: (value: string) => void;
  eventProperties: Record<string, string | number | boolean | null>;
}) => {
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const { isAuthorized, loading, bookingNo } = state;
  const [authorizationTries, setAuthorizationTries] = useQueryState("authorization");
  return (
    <Button
      variant="outline"
      disabled={isAuthorized || loading}
      onClick={async () => {
        if (bookingNo) {
          dispatch({ type: "loading", value: true });
          const tammResponse = await initiateTamm(bookingNo);
          if (Number(authorizationTries) < 1) {
            void setAuthorizationTries("1");
          }
          if (tammResponse.status === 200) {
            void setDriverAuthId(tammResponse.body.id.toLocaleString());
            dispatch({ type: "tammCodePanel", value: true });
            trackEvent("Tamm Authorization Attempted", {
              ...eventProperties,
              attempt_count: Number(authorizationTries),
              tamm_error: null,
              validation_error: null,
            });
          } else {
            dispatch({ type: "tammError", value: tammResponse.body as ErrorSchema });
            trackEvent("Tamm Authorization Attempted", {
              ...eventProperties,
              attempt_count: Number(authorizationTries),
              tamm_error: tammResponse?.body?.desc?.split(".")?.join(" "),
              validation_error: null,
            });
            if (tammResponse?.body) {
              toast({
                title: t("tamm.failedTitle"),
                description: tammResponse?.body?.desc?.split(".")?.join(" "),
                variant: "destructive",
              });
            }
          }
          dispatch({ type: "loading", value: false });
        }
      }}
    >
      {loading ? <LoadingSpinner /> : t("tajeer.authorizeCustomer")}
    </Button>
  );
};

const TammOTPCode = ({
  state,
  dispatch,
  eventProperties,
}: {
  state: State;
  dispatch: (value: Action) => void;
  eventProperties: Record<string, string | number | boolean | null>;
}) => {
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const codeRef = useRef<HTMLInputElement>(null);
  const { bookingNo } = state;
  const [authorizationTries] = useQueryState("authorization");
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  return (
    <div className="mt-4 space-y-4 rounded-lg bg-gray-50 p-6">
      <div>
        <h4 className="text-lg font-semibold">{t("otp.enterCode")}</h4>
        <p className="break-all text-sm text-gray-500">{t("otp.codeSent")}</p>
      </div>

      <div className="flex gap-3">
        <Input ref={codeRef} name="code" type="text" placeholder="Enter code" className="flex-1" />
        <Button
          disabled={state.loading}
          onClick={async () => {
            // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
            const code = codeRef?.current?.value;
            if (bookingNo && code) {
              dispatch({ type: "loading", value: true });
              const validateResponse = await validateTamm(bookingNo, code);
              // if (validateResponse.status === 200) {
              //    dispatch({ type: "tammError", value: null });
              //    dispatch({ type: "tammCodePanel", value: false });
              //    dispatch({ type: "tajeerError", value: null });
              // } else
              if (validateResponse.status !== 200) {
                dispatch({ type: "loading", value: false });
                dispatch({ type: "tammError", value: validateResponse.body as ErrorSchema });
                trackEvent("Tamm OTP Entered", {
                  ...eventProperties,
                  attempt_count: Number(authorizationTries),
                  tamm_error: validateResponse?.body?.desc?.split(".")?.join(" "),
                  validation_error: null,
                  tamm_status: "Fail",
                  otp: code,
                });
                if (validateResponse?.body) {
                  toast({
                    title: t("tamm.failedTitle"),
                    description: validateResponse?.body?.desc?.split(".")?.join(" "),
                    variant: "destructive",
                  });
                }
              } else {
                trackEvent("Tamm OTP Entered", {
                  ...eventProperties,
                  attempt_count: Number(authorizationTries),
                  tamm_error: null,
                  validation_error: null,
                  tamm_status: "Success",
                  otp: code,
                });
              }
            }
            void setAuthorizationMode("Tamm");
          }}
          className="bg-[#BED754] text-black hover:bg-[#BED754]/90"
        >
          {t("otp.validateCode")} {state.loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        </Button>
      </div>
    </div>
  );
};

const OTPCode = ({
  state,
  dispatch,
  setSuccesModal,
  eventProperties,
}: {
  state: State;
  dispatch: (value: Action) => void;
  setSuccesModal: (value: string) => void;
  eventProperties: Record<string, string | number | boolean | null>;
}) => {
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const codeRef = useRef<HTMLInputElement>(null);
  const [authorizationTries] = useQueryState("authorization");
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  const { bookingNo } = state;
  return (
    <div className="mt-4 space-y-4 rounded-lg bg-gray-50 p-6">
      <div>
        <h4 className="text-lg font-semibold">{t("otp.enterCode")}</h4>
        <p className="break-all text-sm text-gray-500">{t("otp.codeSent")}</p>
      </div>

      <div className="flex gap-3">
        <Input ref={codeRef} name="code" type="text" placeholder="Enter code" className="flex-1" />
        <Button
          onClick={async () => {
            // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
            const code = codeRef?.current?.value;
            if (bookingNo && code) {
              dispatch({ type: "loading", value: true });
              const validateResponse = await validateTajeer(bookingNo, code);
              if (validateResponse.status === 200) {
                void setSuccesModal("true");
                trackEvent("Tajeer OTP Entered", {
                  ...eventProperties,
                  tajeer_contract_number: null,
                  attempt_count: Number(authorizationTries),
                  tajeer_error: null,
                  validation_error: null,
                  tajeer_status: "Success",
                  otp: code,
                });
              } else {
                dispatch({ type: "tajeerError", value: validateResponse.body as ErrorSchema });
                trackEvent("Tajeer OTP Entered", {
                  ...eventProperties,
                  tajeer_contract_number: null,
                  attempt_count: Number(authorizationTries),
                  tajeer_error: validateResponse?.body?.desc?.split(".")?.join(" "),
                  validation_error: null,
                  tajeer_status: "Fail",
                  otp: code,
                });
                if (validateResponse?.body) {
                  toast({
                    title: t("tajeer.failedTitle"),
                    description: validateResponse?.body?.desc?.split(".")?.join(" "),
                    variant: "destructive",
                  });
                }
              }
              void setAuthorizationMode("Tajeer");
              dispatch({ type: "loading", value: false });
            }
          }}
          className="bg-[#BED754] text-black hover:bg-[#BED754]/90"
        >
          {t("otp.validateCode")}
        </Button>
      </div>
    </div>
  );
};

const ResendOTP = ({ state, dispatch }: { state: State; dispatch: (value: Action) => void }) => {
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const { bookingNo } = state;
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  return (
    <div className="mt-4 rounded-lg bg-gray-50 p-6">
      <div className="mb-4 flex w-full items-center justify-between">
        <div>
          <h4 className="text-base font-medium text-gray-700">{t("otp.expired")}</h4>
          <p className="break-all font-mono text-gray-900">{t("otp.expiredDescription")}</p>
          <Button
            onClick={async () => {
              dispatch({ type: "loading", value: true });
              if (!bookingNo) {
                return;
              }
              const response = await resendOtp(bookingNo);
              if (response.status === 200) {
                toast({
                  title: t("otp.resentTitle"),
                  description: t("otp.resentDescription"),
                  variant: "success",
                });
                dispatch({ type: "showCodePanel", value: true });
              } else {
                toast({
                  title: t("otp.resendFailedTitle"),
                  description: t("otp.resendFailedDescription"),
                  variant: "destructive",
                });
              }
              void setAuthorizationMode("Tajeer");
              dispatch({ type: "loading", value: false });
            }}
            className="bg-[#BED754] text-black hover:bg-[#BED754]/90"
          >
            {t("otp.resend")}
          </Button>
        </div>
      </div>
    </div>
  );
};

const TajeerWebLink = ({ state }: { state: State }) => {
  const t = useTranslations("authorization");
  return (
    <div className="flex">
      <h4 className="text-lg font-semibold">{t("tajeer.enterContractNumber")}</h4>
      <a href={state.tajeerLink} target="_blank" className="ml-auto flex items-center gap-2 text-sm text-blue-600">
        {t("tajeer.openTajeerWebsite")} <SquareArrowOutUpRight size={18} />
      </a>
    </div>
  );
};

const ManualCode = ({
  state,
  dispatch,
  setSuccesModal,
  eventProperties,
}: {
  state: State;
  dispatch: (value: Action) => void;
  setSuccesModal: (value: string) => void;
  eventProperties: Record<string, string | number | boolean | null>;
}) => {
  const tajeerRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const t = useTranslations("authorization");
  const [authorizationTries] = useQueryState("authorization");
  const [, setAuthorizationMode] = useQueryState("authorization_mode");

  const { loading, bookingNo } = state;
  return (
    <div className="mt-4 space-y-4 rounded-lg bg-gray-50 p-6">
      <TajeerWebLink state={state} />

      <div className="flex gap-3">
        <Input
          disabled={loading}
          ref={tajeerRef}
          name="tajeer-contract"
          type="text"
          placeholder="Tajeer Contract Number"
          className="flex-1"
        />
        <Button
          disabled={loading}
          className="bg-[#BED754] text-black hover:bg-[#BED754]/90"
          onClick={async () => {
            // eslint-disable-next-line @typescript-eslint/non-nullable-type-assertion-style
            const tajeerContract = tajeerRef?.current?.value;
            if (bookingNo && tajeerContract) {
              dispatch({ type: "loading", value: true });
              const validateResponse = await initiateTajeer(bookingNo, tajeerContract);
              dispatch({ type: "loading", value: false });
              if (validateResponse.status !== 200) {
                trackEvent("Tajeer Manual Authorization", {
                  ...eventProperties,
                  attempt_count: Number(authorizationTries),
                  validation_error: null,
                  tajeer_error: (validateResponse.body as ErrorSchema)?.desc ?? "",
                  tajeer_status: "Fail",
                  tajeer_contract_number: tajeerContract,
                });
                toast({
                  title: "FAILED",
                  variant: "destructive",
                  description: (validateResponse.body as ErrorSchema)?.desc ?? "",
                });
              }
              if ((validateResponse.body as ITajeerAgreement).status !== "FAILED") {
                void setSuccesModal("true");
                trackEvent("Tajeer Manual Authorization", {
                  ...eventProperties,
                  tajeer_contract_number: tajeerContract,
                  attempt_count: Number(authorizationTries),
                  tajeer_error: null,
                  validation_error: null,
                  tajeer_status: "Success",
                });
              } else {
                dispatch({
                  type: "tajeerError",
                  value: {
                    code: "FAILED",
                    reqId: "",
                    desc: (validateResponse.body as ITajeerAgreement)?.metadata?.failureReason?.desc ?? "",
                  },
                });
                trackEvent("Tajeer Manual Authorization", {
                  ...eventProperties,
                  tajeer_contract_number: tajeerContract,
                  attempt_count: Number(authorizationTries),
                  tajeer_error: (validateResponse.body as ITajeerAgreement)?.metadata?.failureReason?.desc ?? "",
                  validation_error: null,
                  tajeer_status: "Fail",
                });
                if (validateResponse?.body) {
                  toast({
                    title: t("tajeer.failedTitle"),
                    description: (validateResponse?.body as ITajeerAgreement)?.metadata?.failureReason?.desc,
                    variant: "destructive",
                  });
                }
              }
            }
            void setAuthorizationMode("Tajeer");
          }}
        >
          {loading ? <LoadingSpinner /> : t("tajeer.submit")}
        </Button>
      </div>
    </div>
  );
};
