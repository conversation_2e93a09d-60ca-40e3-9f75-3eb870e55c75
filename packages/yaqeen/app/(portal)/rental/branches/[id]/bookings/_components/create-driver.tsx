"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { type Country, CountryDropdown } from "@/components/ui/country-dropdown";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/lib/hooks/use-toast";
import { driverAttachOrCreate } from "@/lib/actions";
import { getFullDate, isValidDate, makeDateValid, trackEvent } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import { capitalize } from "lodash-es";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { startTransition, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { type z } from "zod";
import { useLocale, useTranslations } from "next-intl";

import { IFRAME_URL } from "../[bookingId]/constants";
import { driverFormSchema } from "../[bookingId]/driver-details/schema";
import { type IDriverFormValues } from "../[bookingId]/driver-details/types";
import { useProgressBar } from "@/components/progress-bar";
import { ID_TYPES_DROPDOWN, type ID_TYPES_DROPDOWN_TYPE } from "./constants";
import { useQueryState } from "nuqs";
import clsx from "clsx";
import { DateHandler } from "../[bookingId]/driver-details/_components/date-handler";
import TammCheck from "@/app/(portal)/rental/_components/tamm-check";
import { useConsolidatedDriverProperties } from "@/lib/hooks/useConsolidatedDriverProperties";

type DriverFormValues = z.infer<typeof driverFormSchema>;
// create a type which dont have driverUid, from IDriverFormValues
type CreateDriverFormValues = Omit<IDriverFormValues, "driverUid">;

interface CreateDriverProps {
  countries: Country[];
}

// Add new component for required field label
const RequiredFieldLabel: React.FC<{ children: React.ReactNode; required?: boolean }> = ({
  children,
  required = true,
}) => (
  <div className="flex gap-1">
    {children}
    {required && <span className="text-red-500">*</span>}
  </div>
);

export default function CreateDriver({ countries: _countries }: CreateDriverProps) {
  const countries = _countries;
  const router = useRouter();
  const { toast } = useToast();
  const progress = useProgressBar();
  const searchParams = useSearchParams();
  const dropOffDateTime = searchParams.get("dropOffDateTime");
  const getDriverProperties = useConsolidatedDriverProperties();

  const locale = useLocale();
  const t = useTranslations("drivers");
  const tErr = useTranslations();

  const [, setDriverName] = useQueryState("driverName", {
    shallow: false,
  });
  const [, setDriverMode] = useQueryState("driverMode", {
    shallow: false,
  });
  const [, setDriverUid] = useQueryState("driverUid", {
    shallow: false,
  });
  const [, setDriverTitle] = useQueryState("driverTitle", {
    shallow: false,
  });

  const quoteId = searchParams.get("quoteId");
  const defaultNationality = "Saudi Arabia";

  const findCountryId = (countryName: string) => {
    return countries.find((country) => country.name === countryName)?.id ?? 2;
  };

  const findCountry = (countryName: string) => {
    return countries.find((country) => country.name === countryName) ?? countries[0]!;
  };

  useEffect(() => {
    if (countries.length > 0) {
      const defaultCountry = findCountry(defaultNationality);
      const filteredTypes = updateIdType(defaultCountry);

      // Set initial ID type based on default nationality
      if (defaultNationality === "Saudi Arabia") {
        form.setValue("idType", "SAUDI_NATIONAL");
      } else if (isGCCCountry(defaultCountry)) {
        form.setValue("idType", "GCC");
      } else {
        form.setValue("idType", "VISITOR");
      }
    }
  }, [countries]);

  const defaultValues: Partial<DriverFormValues> = {
    title: "Mr",
    firstName: "",
    lastName: "",
    email: "",
    nationality: countries.length > 0 ? "Saudi Arabia" : "",
    countryCode: "",
    mobileNumber: "",
    idType: "SAUDI_NATIONAL",
    dob: "",
    hijrahDob: "",
    address: "",
    documentNo: "",
    idIssuedCountry: countries.length > 0 ? "Saudi Arabia" : "",
    documentExpiry: "",
    licenseNo: "",
    licenseCountry: countries.length > 0 ? "Saudi Arabia" : "",
    licenseExpiry: "",
    borderNumber: "",
  };

  const form = useForm<DriverFormValues>({
    resolver: zodResolver(driverFormSchema),
    defaultValues,
    mode: "onTouched",
    criteriaMode: "all",
  });

  // console form error state
  console.log("Form errors:", form.formState.errors);

  // Handle blur event to validate empty fields
  const handleBlur = (fieldName: keyof DriverFormValues) => {
    form.trigger(fieldName).catch((_error) => {
      console.error("Validation error occurred");
    });
  };

  // Update form values when countries are loaded
  useEffect(() => {
    if (countries.length > 0) {
      form.setValue("nationality", "Saudi Arabia");
      form.setValue("idIssuedCountry", "Saudi Arabia");
      form.setValue("licenseCountry", "Saudi Arabia");
    }
  }, [countries, form]);

  // Update ID type validation
  useEffect(() => {
    const idType = form.getValues("idType");
    const documentNo = form.getValues("documentNo");

    // If document number exists and ID type is Saudi or Resident (Iqama)
    if (documentNo && (idType === "SAUDI_NATIONAL" || idType === "RESIDENT")) {
      if (documentNo.length !== 10) {
        form.setError("documentNo", {
          type: "manual",
          message: t("errors.Document number must be exactly 10 digits for Saudi ID/Iqama"),
        });
      } else {
        // Clear the error if length is correct
        form.clearErrors("documentNo");
      }
    }
  }, [form.watch("idType"), form.watch("documentNo"), t]);

  // Add validation to document number field change
  const handleDocumentNoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const idType = form.getValues("idType");

    // Update the form value
    form.setValue("documentNo", value);

    // For Saudi nationals and Iqama holders, update license number
    if (idType === "SAUDI_NATIONAL" || idType === "RESIDENT") {
      // Also update the license number to match the document number
      form.setValue("licenseNo", value);

      // Validate length for both fields
      if (value.length !== 10) {
        form.setError("documentNo", {
          type: "manual",
          message: t("errors.Document number must be exactly 10 digits for Saudi ID/Iqama"),
        });
        form.setError("licenseNo", {
          type: "manual",
          message: t("errors.Document number must be exactly 10 digits for Saudi ID/Iqama"),
        });
      } else {
        form.clearErrors("documentNo");
        form.clearErrors("licenseNo");
      }
    }
  };

  async function onSubmit(data: DriverFormValues) {
    data.mobileNumber = data.mobileNumber.replace(`${data.countryCode}`, "");
    const countryCode = data.countryCode.toString().replace("+", "");
    const nationalityCountryId = findCountryId(data.nationality);
    const idIssuedCountryId = findCountryId(data.idIssuedCountry ?? data.nationality);
    const licenseCountryId = findCountryId(data.licenseCountry ?? data.nationality);
    const isVisitor = data.idType === "VISITOR";

    const _data: CreateDriverFormValues = {
      title: data.title,
      firstName: data.firstName,
      lastName: data.lastName,
      mobileNumber: data.mobileNumber ?? "",
      countryCode: Number(countryCode),
      email: data.email ?? "",
      dob: makeDateValid(data.dob ?? ""),
      hijrahDob: makeDateValid(data.hijrahDob ?? ""),
      documents: [],
      nationality: {
        id: nationalityCountryId,
      },
      address: {
        street: data.address,
      },
      idType: data.idType,
      metadata: {
        borderNumber: data.borderNumber ?? "",
      },
    };

    const _idType =
      data.idType === "SAUDI_NATIONAL" || data.idType === "GCC"
        ? "ID"
        : data.idType === "RESIDENT"
          ? "IQAMA"
          : data.idType === "VISITOR"
            ? "PASSPORT"
            : "PASSPORT";

    if (!isVisitor) {
      delete _data.metadata;
    }

    const document = {
      documentNo: data.documentNo || "",
      expiry: makeDateValid(data.documentExpiry ?? ""),
      hijrahExpiry: "",
      issuedPlace: {
        id: idIssuedCountryId,
      },
      type: _idType,
      version: 1,
    };

    const license = {
      documentNo: data.licenseNo || "",
      expiry: makeDateValid(data.licenseExpiry),
      hijrahExpiry: "",
      issuedPlace: {
        id: licenseCountryId,
      },
      type: "LICENSE",
    };

    _data.documents = [document, license];

    try {
      // iterate over the _data and remove any undefined or empty fields at all
      const cleanData = Object.fromEntries(
        Object.entries(_data).filter(([_, value]) => {
          if (typeof value === "object" && value !== null) {
            // For objects, check if they have any non-empty properties
            return Object.keys(value).length > 0;
          }
          // For other types, check if they are not empty or undefined
          return value !== undefined && value !== "" && value !== null;
        })
      );
      console.log("Submitting driver data:", JSON.stringify(cleanData, null, 2));
      // return
      // @ts-expect-error TODO: Fix type error
      const response = await driverAttachOrCreate(cleanData, quoteId!);

      if (response.status === 500) {
        toast({
          variant: "destructive",
          title: t("errors.serverFailure"),
          duration: 3000,
        });
        return;
      }

      if (response.status !== 200) {
        toast({
          variant: "destructive",
          title: t("errors.failed"),
          description: capitalize(response.body.desc ?? t("errors.failedToCreateDriver")),
          duration: 3000,
        });
        return;
      }

      if (response.status === 200) {
        progress.start();
        const driverProperties = getDriverProperties(response.body);

        trackEvent("New Driver Created", driverProperties);

        void setDriverUid(response.body.driverUId);
        void setDriverName(`${data.firstName} ${data.lastName}`);
        void setDriverMode("viewDetail");
        void setDriverTitle(data.title);
        startTransition(() => {
          setTimeout(() => {
            window.location.reload();
          }, 500);
        });
        progress.done();

        toast({
          variant: "success",
          title: t("success.driverCreated"),
          description: "",
          duration: 3000,
        });
      }
      console.log("Driver created successfully", response);
    } catch (error) {
      console.error("Error creating driver:", JSON.stringify(error));
    }
  }

  const isGCCCountry = (country: Country) => {
    const gccCountries = ["Bahrain", "Kuwait", "Oman", "Qatar", "United Arab Emirates"];
    return gccCountries.includes(country.name);
  };

  const updateIdType = (country: Country) => {
    if (country.name === "Saudi Arabia") {
      // For Saudi nationals, only show SAUDI_NATIONAL
      return ID_TYPES_DROPDOWN.filter((type) => type.value === "SAUDI_NATIONAL");
    } else if (isGCCCountry(country)) {
      // For GCC countries, show GCC and VISITOR (PASSPORT)
      return ID_TYPES_DROPDOWN.filter((type) => ["GCC", "RESIDENT", "VISITOR"].includes(type.value)).sort((a, b) => {
        // Make sure GCC appears first in the dropdown
        if (a.value === "GCC") return -1;
        if (b.value === "GCC") return 1;
        return 0;
      });
    } else {
      // For other countries, show RESIDENT and VISITOR (PASSPORT)
      return ID_TYPES_DROPDOWN.filter((type) => ["RESIDENT", "VISITOR"].includes(type.value)).sort((a, b) => {
        // Make sure RESIDENT appears first in the dropdown
        if (a.value === "RESIDENT") return -1;
        if (b.value === "RESIDENT") return 1;
        return 0;
      });
    }
  };

  // Get localized ID type label
  const getLocalizedIdType = (idType: string) => {
    const matchingType = ID_TYPES_DROPDOWN.find((type) => type.value === idType);

    if (matchingType) {
      if (matchingType.translationKey) {
        return t(`idTypes.${matchingType.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
          defaultValue: matchingType.label,
        });
      }
      return matchingType.label;
    }

    return capitalize(idType.split("_").join(" "));
  };

  // Update the shouldShowField function to be more specific
  const shouldShowField = (fieldName: keyof DriverFormValues) => {
    const idType = form.getValues("idType");
    const isSaudi = form.getValues("nationality") === "Saudi Arabia";
    const isResident = idType === "RESIDENT";

    switch (fieldName) {
      case "email":
      case "licenseNo":
      case "licenseCountry":
        return !isSaudi && !isResident;
      case "hijrahDob":
        return isSaudi;
      case "dob":
        return !isSaudi;
      case "documentExpiry":
        return !isSaudi && !isResident;
      case "borderNumber":
        return idType === "VISITOR";
      default:
        return true;
    }
  };

  const isFieldRequired = (fieldName: keyof DriverFormValues) => {
    if (!shouldShowField(fieldName)) return false;

    const nationality = form.getValues("nationality");
    const idType = form.getValues("idType");

    // Fields that are always required when shown
    const alwaysRequired = ["firstName", "lastName", "mobileNumber", "nationality", "idType", "documentNo", "address"];
    if (alwaysRequired.includes(fieldName)) return true;

    // Conditional requirements
    if (fieldName === "documentExpiry" && (idType === "VISITOR" || "GCC")) return true;
    if (fieldName === "borderNumber" && idType === "VISITOR") return true;
    if (fieldName === "hijrahDob" && idType === "SAUDI_NATIONAL") return true;
    if (fieldName === "dob" && idType !== "SAUDI_NATIONAL") return true;
    if (
      ["email", "licenseNo", "licenseCountry"].includes(fieldName) &&
      idType !== "SAUDI_NATIONAL" &&
      idType !== "RESIDENT"
    )
      return true;

    return false;
  };

  // Add this helper method for error message localization
  const getLocalizedErrorMessage = (error: string | undefined) => {
    if (!error) return undefined;
    // @ts-expect-error TODO
    return tErr(error, { defaultValue: error });
  };

  // useEffect(() => {
  //   void form.trigger();
  // }, []);

  useEffect(() => {
    if (getLocalizedErrorMessage(form.formState.errors.title?.message)) {
      form.setValue("title", capitalize("Mr"));
    }
  }, [getLocalizedErrorMessage(form.formState.errors.title?.message)]);

  return (
    <div className="m-4 flex flex-col gap-x-3 rounded-md bg-slate-100">
      <p className="p-4 text-base font-bold text-slate-900">{t("actions.createNewDriver")}</p>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="w-full">
          {/* Form fields container with flex-wrap */}
          <div className="flex w-full flex-wrap gap-6 p-4">
            {/* First name field */}
            <div className="flex min-w-[300px] flex-1">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      <RequiredFieldLabel required={isFieldRequired("firstName")}>
                        {t("fields.firstName")}
                      </RequiredFieldLabel>
                    </FormLabel>
                    <div className="flex items-center overflow-hidden rounded-md border">
                      <Select onValueChange={(value) => form.setValue("title", capitalize(value))} defaultValue="MR">
                        <SelectTrigger className="w-16 rounded-r-none border-none focus:outline-none">
                          <SelectValue placeholder="Mr" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="MR">Mr</SelectItem>
                          <SelectItem value="MRS">Mrs</SelectItem>
                        </SelectContent>
                      </Select>

                      <Input
                        {...field}
                        placeholder={t("fields.firstName")}
                        className="flex-1 rounded-l-none border-none !pl-0 focus:outline-none"
                        onBlur={() => handleBlur("firstName")}
                      />
                    </div>
                    <FormMessage>{getLocalizedErrorMessage(form.formState.errors.firstName?.message)}</FormMessage>
                  </FormItem>
                )}
              />
            </div>

            {/* Last name field */}
            <div className="flex min-w-[300px] flex-1">
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      <RequiredFieldLabel required={isFieldRequired("lastName")}>
                        {t("fields.lastName")}
                      </RequiredFieldLabel>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t("fields.lastName")} onBlur={() => handleBlur("lastName")} />
                    </FormControl>
                    <FormMessage>{getLocalizedErrorMessage(form.formState.errors.lastName?.message)}</FormMessage>
                  </FormItem>
                )}
              />
            </div>

            {/* Mobile number field */}
            <div className="flex min-w-[300px] flex-1">
              <FormField
                control={form.control}
                name="mobileNumber"
                render={({ field }) => {
                  return (
                    <FormItem className="w-full">
                      <FormLabel>
                        <RequiredFieldLabel required={isFieldRequired("mobileNumber")}>
                          {t("fields.mobile")}
                        </RequiredFieldLabel>
                      </FormLabel>
                      <FormControl>
                        <PhoneInput
                          value={field.value}
                          onCountryChange={(value) => {
                            form.setValue("mobileNumber", value ?? "");
                            form.setValue("countryCode", value ?? "");
                            handleBlur("mobileNumber");
                          }}
                          onChange={(value: { countryCode: string; phoneNumber: string }) => {
                            form.setValue("mobileNumber", `${value?.countryCode}${value?.phoneNumber}`);
                            form.setValue("countryCode", value?.countryCode);
                          }}
                          onBlur={() => handleBlur("mobileNumber")}
                          placeholder="+966 50 123 4567"
                        />
                      </FormControl>
                      <FormMessage>{getLocalizedErrorMessage(form.formState.errors.mobileNumber?.message)}</FormMessage>
                    </FormItem>
                  );
                }}
              />
            </div>

            {/* Nationality field */}
            <div className="flex min-w-[300px] flex-1">
              <FormField
                control={form.control}
                name="nationality"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      <RequiredFieldLabel required={isFieldRequired("nationality")}>
                        {t("fields.nationality")}
                      </RequiredFieldLabel>
                    </FormLabel>
                    <CountryDropdown
                      placeholder={t("fields.nationality")}
                      options={countries}
                      defaultValue={field.value}
                      onChange={(country) => {
                        if (!country) return;

                        field.onChange(country.name);

                        // Set appropriate ID type based on nationality
                        if (country.name === "Saudi Arabia") {
                          form.setValue("idType", "SAUDI_NATIONAL");
                        } else if (isGCCCountry(country)) {
                          form.setValue("idType", "GCC"); // Set default to GCC for GCC countries
                        } else {
                          form.setValue("idType", "VISITOR"); // Set default to RESIDENT for other countries
                        }

                        // Reset related fields
                        form.setValue("licenseCountry", country.name);
                        form.setValue("idIssuedCountry", country.name);

                        handleBlur("nationality");
                      }}
                    />
                    <FormMessage>{getLocalizedErrorMessage(form.formState.errors.nationality?.message)}</FormMessage>
                  </FormItem>
                )}
              />
            </div>

            {/* ID Type field */}
            <div className="flex min-w-[300px] flex-1">
              <FormField
                control={form.control}
                name="idType"
                render={() => {
                  return (
                    <FormItem className="w-full">
                      <FormLabel>
                        <RequiredFieldLabel required={isFieldRequired("idType")}>
                          {t("fields.idType")}
                        </RequiredFieldLabel>
                      </FormLabel>
                      <div className="flex items-center overflow-hidden rounded-md border">
                        <Select
                          onValueChange={(value) => {
                            if (form.getValues("nationality") === "Saudi Arabia") {
                              form.setValue("idType", "SAUDI_NATIONAL");
                              return;
                            }
                            form.setValue("idType", value);
                          }}
                          value={form.getValues("idType")}
                          disabled={form.getValues("nationality") === "Saudi Arabia"}
                        >
                          <SelectContent>
                            {updateIdType(findCountry(form.getValues("nationality"))).map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.translationKey
                                  ? t(`idTypes.${type.translationKey as ID_TYPES_DROPDOWN_TYPE}`, {
                                      defaultValue: type.label,
                                    })
                                  : type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                          <SelectTrigger className="rounded-r-none border-none focus:outline-none">
                            <SelectValue placeholder={"Select id type"} />
                          </SelectTrigger>
                        </Select>
                      </div>
                      <FormMessage>{getLocalizedErrorMessage(form.formState.errors.idType?.message)}</FormMessage>
                    </FormItem>
                  );
                }}
              />
            </div>

            {/* Document Number field */}
            <div className="flex min-w-[300px] flex-1">
              <FormField
                control={form.control}
                name="documentNo"
                render={({ field }) => {
                  const idtype = form.getValues("idType");
                  const idTypeLabel = getLocalizedIdType(idtype);
                  return (
                    <FormItem className="w-full">
                      <FormLabel>
                        <RequiredFieldLabel required={isFieldRequired("documentNo")}>
                          {idTypeLabel ? (
                            <div
                              className={clsx(
                                "flex gap-x-1 pb-2",
                                locale === "ar" ? "flex-row-reverse justify-end" : "flex-row"
                              )}
                            >
                              <div>{idTypeLabel}</div>
                              <div>{t("fields.number")}</div>
                            </div>
                          ) : (
                            t("fields.idNumber")
                          )}
                        </RequiredFieldLabel>
                      </FormLabel>
                      <FormControl>
                        <Input
                          autoComplete="off"
                          className="!mt-0"
                          placeholder={t("fields.idNumberForm")}
                          {...field}
                          onChange={(e) => handleDocumentNoChange(e)}
                          onBlur={() => handleBlur("documentNo")}
                        />
                      </FormControl>
                      <FormMessage>{getLocalizedErrorMessage(form.formState.errors.documentNo?.message)}</FormMessage>
                    </FormItem>
                  );
                }}
              />
            </div>

            {form.getValues("idType") === "VISITOR" && <TammCheck text={t("tamm.visitorLicenseInfo")} />}

            {/* Conditionally render DOB fields */}
            {shouldShowField("hijrahDob") ? (
              <div className="flex min-w-[300px] flex-1">
                <FormField
                  control={form.control}
                  name="hijrahDob"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <DateHandler
                        label={t("fields.dateOfBirthHijri")}
                        dateType="hijri"
                        value={field.value}
                        onChange={(date) => {
                          form.setValue("hijrahDob", date);
                          if (isValidDate(date)) {
                            const dates = getFullDate("", date);
                            form.setValue("dob", dates.gregorean);
                          }
                          handleBlur("hijrahDob");
                        }}
                        required={isFieldRequired("hijrahDob")}
                        isDateOfBirth={true}
                        error={getLocalizedErrorMessage(form.formState.errors.hijrahDob?.message)}
                      />
                      {/* <FormMessage>{getLocalizedErrorMessage(form.formState.errors.hijrahDob?.message)}</FormMessage> */}
                    </FormItem>
                  )}
                />
              </div>
            ) : (
              shouldShowField("dob") && (
                <div className="flex min-w-[300px] flex-1">
                  <FormField
                    control={form.control}
                    name="dob"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <DateHandler
                          label={t("fields.dateOfBirthGregorian")}
                          dateType="gregorian"
                          value={field.value}
                          onChange={(date) => {
                            form.setValue("dob", date);
                            if (isValidDate(date)) {
                              const dates = getFullDate(date);
                              form.setValue("hijrahDob", dates.hijri);
                            }
                            handleBlur("dob");
                          }}
                          required={isFieldRequired("dob")}
                          isDateOfBirth={true}
                          error={getLocalizedErrorMessage(
                            form.formState.errors.dob?.message || form.formState.errors.hijrahDob?.message
                          )}
                        />
                        {/* <FormMessage >
                          {getLocalizedErrorMessage(form.formState.errors.dob?.message ||
                              form.formState.errors.hijrahDob?.message)}
                        </FormMessage> */}
                      </FormItem>
                    )}
                  />
                </div>
              )
            )}

            {/* Document expiry dates */}
            {shouldShowField("documentExpiry") && (
              <div className="flex min-w-[300px] flex-1">
                <FormField
                  control={form.control}
                  name="documentExpiry"
                  render={({ field }) => {
                    const idtype = form.getValues("idType");
                    const idTypeLabel = getLocalizedIdType(idtype);
                    return (
                      <FormItem className="w-full">
                        <RequiredFieldLabel required={isFieldRequired("documentNo")}>
                          {idTypeLabel ? (
                            <div
                              className={clsx(
                                "flex gap-x-1 pb-2 text-sm",
                                locale === "ar" ? "flex-row-reverse justify-end" : "flex-row"
                              )}
                            >
                              <div>{idTypeLabel}</div>
                              <div>{t("fields.idExpiryDateGregorian")}</div>
                            </div>
                          ) : (
                            <span className="text-sm">{t("fields.idExpiryDateGregorian")}</span>
                          )}
                        </RequiredFieldLabel>
                        <DateHandler
                          label=""
                          dateType="gregorian"
                          value={field.value}
                          onChange={(date) => {
                            form.setValue("documentExpiry", date);
                            handleBlur("documentExpiry");
                          }}
                          required={false}
                          className="!mt-0"
                          // error={getLocalizedErrorMessage(form.formState.errors.documentExpiry?.message)}
                        />
                        <FormMessage>
                          {getLocalizedErrorMessage(form.formState.errors.documentExpiry?.message)}
                        </FormMessage>
                      </FormItem>
                    );
                  }}
                />
              </div>
            )}

            {/* Address field */}
            <div className="flex min-w-[300px] flex-1">
              <FormField
                control={form.control}
                name="address"
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel>
                      <RequiredFieldLabel required={isFieldRequired("address")}>
                        {t("fields.address")}
                      </RequiredFieldLabel>
                    </FormLabel>
                    <FormControl>
                      <Input placeholder={t("fields.address")} {...field} onBlur={() => handleBlur("address")} />
                    </FormControl>
                    <FormMessage>{getLocalizedErrorMessage(form.formState.errors.address?.message)}</FormMessage>
                  </FormItem>
                )}
              />
            </div>

            {/* Email field */}
            {shouldShowField("email") && (
              <div className="flex min-w-[300px] flex-1">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <FormLabel>
                        <RequiredFieldLabel required={isFieldRequired("email")}>{t("fields.email")}</RequiredFieldLabel>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder={t("fields.email")} {...field} onBlur={() => handleBlur("email")} />
                      </FormControl>
                      <FormMessage>{getLocalizedErrorMessage(form.formState.errors.email?.message)}</FormMessage>
                    </FormItem>
                  )}
                />
              </div>
            )}

            {/* Border Number field */}
            {shouldShowField("borderNumber") && (
              <div className="flex w-1/2 min-w-[300px] pr-2">
                <FormField
                  control={form.control}
                  name="borderNumber"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <div className="flex flex-row items-center justify-between">
                        <FormLabel>
                          <RequiredFieldLabel required={isFieldRequired("borderNumber")}>
                            {t("fields.borderNumber")}
                          </RequiredFieldLabel>
                        </FormLabel>
                        <Link
                          href={IFRAME_URL}
                          target="_blank"
                          className="h-auto p-0 text-sm font-normal text-blue-600"
                        >
                          {t("actions.getBorderNumber")}
                        </Link>
                      </div>
                      <FormControl>
                        <Input
                          placeholder={t("fields.borderNumber")}
                          {...field}
                          onBlur={() => handleBlur("borderNumber")}
                        />
                      </FormControl>
                      <FormMessage>{getLocalizedErrorMessage(form.formState.errors.borderNumber?.message)}</FormMessage>
                    </FormItem>
                  )}
                />
              </div>
            )}
          </div>

          <Separator />

          {/* License details section */}
          <div className="flex w-full flex-col">
            <span className="p-4 text-base font-bold text-slate-900">{t("sections.drivingLicense")}</span>

            <div className="flex w-full flex-wrap gap-6 p-4">
              {/* License details fields */}
              {shouldShowField("licenseNo") && (
                <>
                  <div className="flex min-w-[300px] flex-1">
                    <FormField
                      control={form.control}
                      name="licenseNo"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>
                            <RequiredFieldLabel required={isFieldRequired("licenseNo")}>
                              {t("fields.licenseNumber")}
                            </RequiredFieldLabel>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder={t("fields.licenseNumber")}
                              {...field}
                              onBlur={() => handleBlur("licenseNo")}
                            />
                          </FormControl>
                          <FormMessage>
                            {getLocalizedErrorMessage(form.formState.errors.licenseNo?.message)}
                          </FormMessage>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex min-w-[300px] flex-1">
                    <FormField
                      control={form.control}
                      name="licenseCountry"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <FormLabel>
                            <RequiredFieldLabel required={isFieldRequired("licenseCountry")}>
                              {t("fields.country")}
                            </RequiredFieldLabel>
                          </FormLabel>
                          <CountryDropdown
                            placeholder={t("fields.country")}
                            options={countries}
                            defaultValue={field.value}
                            onChange={(country) => {
                              field.onChange(country.name);
                              handleBlur("licenseCountry");
                            }}
                          />
                          <FormMessage>
                            {getLocalizedErrorMessage(form.formState.errors.licenseCountry?.message)}
                          </FormMessage>
                        </FormItem>
                      )}
                    />
                  </div>
                </>
              )}

              <div className="flex w-1/2 min-w-[300px] pr-2">
                <FormField
                  control={form.control}
                  name="licenseExpiry"
                  render={({ field }) => (
                    <FormItem className="w-full">
                      <DateHandler
                        label={t("fields.License Expiry Date - Gregorian")}
                        dateType="gregorian"
                        required
                        value={field.value}
                        onChange={(date) => {
                          form.setValue("licenseExpiry", date);
                          if (!date || !dropOffDateTime) return;

                          const dropoffDate = new Date(parseInt(dropOffDateTime) * 1000);
                          const bufferDate = new Date(dropoffDate.getTime() + 2 * 24 * 60 * 60 * 1000);
                          const licenseDate = new Date(date.split("/").reverse().join("-"));

                          if (licenseDate < bufferDate) {
                            form.setError("licenseExpiry", {
                              type: "manual",
                              message: `License expiry date must be at least 2 days after the drop-off date`,
                            });
                          } else {
                            form.clearErrors("licenseExpiry");
                          }
                        }}
                        // error={getLocalizedErrorMessage(form.formState.errors.licenseExpiry?.message)}
                      />
                      <FormMessage>
                        {getLocalizedErrorMessage(form.formState.errors.licenseExpiry?.message)}
                      </FormMessage>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>

          {/* Footer buttons */}
          <div className="flex w-full justify-end gap-x-2 p-4">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline">{t("actions.cancel")}</Button>
              </DialogTrigger>
              <DialogContent className="p-0 sm:max-w-md">
                <DialogHeader className="p-4">
                  <DialogTitle>{t("dialogs.cancelCreation.title")}</DialogTitle>
                  <DialogDescription>{t("dialogs.cancelCreation.description")}</DialogDescription>
                </DialogHeader>
                <Separator className="" />
                <DialogFooter className="p-4 sm:justify-end">
                  <Button
                    onClick={() => {
                      void setDriverMode("viewDetail");
                      router.back();
                    }}
                    variant="outline"
                  >
                    {t("dialogs.cancelCreation.confirm")}
                  </Button>
                  <DialogClose asChild>
                    <Button type="button">{t("dialogs.cancelCreation.continue")}</Button>
                  </DialogClose>
                </DialogFooter>
              </DialogContent>
            </Dialog>
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <LoadingSpinner className="ml-1 text-slate-800" />
              ) : (
                t("actions.createDriver")
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
