import { api } from "@/api";
import { DataTable } from "@/components/ui/data-table/data-table";
import { bookingSearchParamsCache } from "@/lib/params";
import { getAllBookingTimeRange } from "@/lib/utils";
import { type SearchParams } from "nuqs/server";
import { BOOKING_DATE_TIME, searchFilters } from "./_components/constants";
import Header from "./_components/Header";
import { columns } from "./columns";
import { filters } from "./constants";
import type { IBranch } from "@/api/contracts/branch-contract";
import { getLocale, getTranslations } from "next-intl/server";

type PageProps = {
  searchParams: Promise<SearchParams>;
  params: Promise<Record<string, string>>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  const { id } = await props.params;
  const t = await getTranslations("bookings");

  const branches = await api.branch.getBranchList({
    query: { page: 0, size: 1000 },
  });
  if (branches.status !== 200) {
    throw new Error("Failed to fetch branches");
  }
  const branchResponse: IBranch[] = branches.body.data;
  const branch = branchResponse.find((branch) => branch.id === Number(id));
  const {
    pageSize,
    pageNumber,
    bookingNo,
    mobileNumber,
    driverName,
    status,
    dropOffDateRangeStart,
    pickupDateRangeStart,
    agreementNo,
  } = bookingSearchParamsCache.parse(searchParams);

  const ORDER = "desc";
  const { start, end } = getAllBookingTimeRange(pickupDateRangeStart);
  const { start: dropOffStart, end: dropOffEnd } = getAllBookingTimeRange(dropOffDateRangeStart);

  const [bookings] = await Promise.all([
    api.booking.getUpcomingBookings({
      query: {
        ...(start && { "pickupDateRange.start": start }),
        ...(end && { "pickupDateRange.end": end }),
        ...(dropOffStart && { "dropOffDateRange.start": dropOffStart }),
        ...(dropOffEnd && { "dropOffDateRange.end": dropOffEnd }),
        order: ORDER,
        sort: BOOKING_DATE_TIME,
        page: pageNumber,
        size: pageSize,
        bookingNo: bookingNo,
        mobileNumber: mobileNumber,
        driverName: driverName,
        status: status,
        agreementNo: agreementNo,
        pickupBranchId: Number(id!),
      },
    }),
  ]);

  if (bookings.status !== 200) {
    throw new Error("Error");
  }

  return (
    <div>
      <Header
        branch={branch}
        activeTab={{
          label: t("allBookings"),
          count: 0,
        }}
      />
      <div className="flex flex-col px-6">
        <DataTable
          searchPlaceholder={t("searchPlaceholder")}
          columns={columns}
          filters={filters}
          searchFilters={searchFilters}
          rowClickId={"id"}
          baseRedirectPath={`/rental/branches/${id}/bookings/`}
          data={{
            data: bookings.body.data.map((booking) => ({
              ...booking,
              agreementNo: booking.agreementNo?.toString(),
            })),
            total: bookings.body.total,
          }}
          emptyMessage={t("emptyMessage")}
        />
      </div>
    </div>
  );
}
