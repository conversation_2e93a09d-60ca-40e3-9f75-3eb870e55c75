import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { api } from "@/api";
import { useTranslations } from "next-intl";
import { getTranslations } from "next-intl/server";

const LoyaltyCard = ({ children }: { children: React.ReactNode }) => {
  const t = useTranslations("createAgreement");

  return (
    <Card className="flex flex-col shadow">
      <CardHeader className="px-4">
        <CardTitle className="text-lg font-bold">{t("bookingDetails.loayltyProgramTitle")}</CardTitle>
      </CardHeader>
      <Separator />
      {children}
    </Card>
  );
};

export default async function LoyaltyProgram({ bookingId }: { bookingId: string }) {
  const t = await getTranslations("createAgreement");

  try {
    // First get the booking data to extract referenceNo
    const bookingResponse = await api.bookingDetails.getBookingById({
      params: {
        id: Number(bookingId),
      },
    });

    if (bookingResponse?.status !== 200) {
      console.error(`Failed to fetch booking: ${bookingResponse.status}`);
      return <></>;
    }

    // Now make all remaining calls at once
    const loyalty = await api.bookingDetails.getLoyaltyByReference({
      params: {
      referenceNo: String(bookingResponse.body.referenceNo),
      },
    });

    if (loyalty?.status === 404) {
      return <></>;
    }

    if (loyalty?.status !== 200) {
      console.error(`Failed to fetch loyalty data: ${loyalty.status}`);
      return <></>;
    }

    const { provider, customerIdentifier, value, valueType } = loyalty.body;

    return (
      <LoyaltyCard>
        <CardContent className="flex w-full flex-col items-center p-0">
          <div className="flex w-full p-4 text-sm">
            <div className="flex w-full flex-col gap-y-2">
              <span className=" text-slate-500">{t("bookingDetails.loayltyProgram")}</span>
              <span className=" text-slate-900 ">{provider}</span>
            </div>
            <div className="flex w-full flex-col gap-y-2">
              <span className=" text-slate-500">{t("bookingDetails.accountNumber")}</span>
              <span className=" text-slate-900 ">{customerIdentifier}</span>
            </div>
            <div className="flex w-full flex-col gap-y-2">
              <span className=" text-slate-500">{t("bookingDetails.points")}</span>
              <span className=" text-slate-900 ">{value ?? valueType}</span>
            </div>
          </div>
        </CardContent>
      </LoyaltyCard>
    );
  } catch (error) {
    console.error("Error in LoyaltyProgram component:", error);
    // Return empty fragment to prevent page crash
    return <></>;
  }
}
