"use client";

import React from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { DotsThree, Calendar, MoneyIcon } from "@phosphor-icons/react/dist/ssr";
import { CollectPaymentWrapper } from "@/components/collect-payment-wrapper";
import { useTranslations } from "next-intl";
import { PencilSimpleIcon } from "@phosphor-icons/react";
import { CopyLinkButton } from "../../_components/CopyLinkButton";

interface DropdownItemsProps {
  bookingId: string;
  branchId: number;
  finalAgreementNumber: string;
  bookingStatus: string;
  pickupBranchId: string;
  remainingAmount: number;
  editBookingAllowed: boolean;
}

export default function DropdownItems({
  bookingId,
  branchId,
  finalAgreementNumber,
  bookingStatus,
  pickupBranchId,
  remainingAmount,
  editBookingAllowed,
}: DropdownItemsProps) {
  const t = useTranslations("bookingDetail");
  const tableT = useTranslations("DataTable.RowActions");

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <div className="flex items-center justify-end gap-x-1">
          <Button variant="outline">
            <DotsThree className="h-4 w-4" />
          </Button>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {["UPCOMING", "ONGOING", "LATE_RETURN", "SUSPENDED"].includes(bookingStatus) && (
          <CollectPaymentWrapper
            bookingId={bookingId}
            branchId={parseInt(pickupBranchId) ?? 0}
            remainingAmount={remainingAmount}
          >
            <DropdownMenuItem
              className="flex cursor-pointer items-center justify-start"
              onClick={(e) => e.preventDefault()}
            >
              <MoneyIcon className="mr-2 h-4 w-4" />
              {tableT("Collect payment")}
            </DropdownMenuItem>
          </CollectPaymentWrapper>
        )}
        {["ONGOING", "LATE_RETURN"].includes(bookingStatus ?? "") && (
          <a
            className="flex items-center gap-2 p-2"
            href={`/rental/branches/${branchId}/replace-vehicle/${finalAgreementNumber}/inspection-details`}
          >
            <Calendar className="h-4 w-4" /> {t("cta.replace_vehicle")}
          </a>
        )}
        {["UPCOMING"].includes(bookingStatus ?? "") && (
          <>
            {editBookingAllowed ? (
              <DropdownMenuItem disabled className="flex cursor-pointer items-center gap-2">
                <PencilSimpleIcon className="h-4 w-4" />
                {t("cta.edit")}
              </DropdownMenuItem>
            ) : null}
            <CopyLinkButton variant="dropdown" />
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
