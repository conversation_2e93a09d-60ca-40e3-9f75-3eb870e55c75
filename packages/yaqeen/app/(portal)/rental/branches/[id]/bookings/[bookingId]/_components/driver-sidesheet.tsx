"use client";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Buildings, EnvelopeSimple, Phone, Clock, CalendarMinus, Key, EyeSlash } from "@phosphor-icons/react/dist/ssr";
import { differenceInDays, format } from "date-fns";
import { calculateAgeInYears, formatToCamel, getBadgeColor, getFullDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import CopyLink from "@/components/customized/copy-link";
import OpenLink from "@/components/customized/open-link";
import { Fragment } from "react";
import OpenLinkW from "./Outlink";
import { type IDocument } from "../types";
import { useLocale, useTranslations } from "next-intl";
import type { IBookingDetails } from "@/api/contracts/booking/booking-contract";
import type { Driver } from "@/api/contracts/booking/driver-details-contract";

interface DriverProfileSheetProps {
  bookings: IBookingDetails[];
  bookingCount: {
    totalAgreementCount: number;
    totalNoShowCount: number;
  };
  driver: Driver;
}

export function DriverProfileSheet({ bookings, bookingCount, driver }: DriverProfileSheetProps) {
  const t = useTranslations("driverProfile");
  const bookingT = useTranslations("bookings.filters");
  const locale = useLocale() as "en" | "ar";

  try {
    const {
      firstName = "",
      lastName = "",
      countryCode = 966,
      mobileNumber = "",
      email = "",
      dob = "",
      hijrahDob = "",
      idType = "",
      nationality = { code: 0, name: { en: "N/A", ar: "غير متاح" } },
    } = driver;

    const age = dob ? calculateAgeInYears(dob) : t("notAvailable");

    const license = driver?.documents.find((doc) => doc.type === "LICENSE") ?? {
      code: 0,
      type: "LICENSE",
      documentNo: "XXX",
      expiry: "",
      hijrahExpiry: "",
      issuedPlace: { code: 0, name: { en: "", ar: "" } },
    };

    let document: IDocument = {} as IDocument;

    switch (idType) {
      case "SAUDI_NATIONAL":
      case "GCC":
        document = driver.documents.find((doc) => doc.type === "ID" || doc.type === "NATIONAL_ID") ?? document;
        break;
      case "RESIDENT":
        document = driver.documents.find((doc) => doc.type === "IQAMA") ?? document;
        break;
      case "VISITOR":
        document = driver.documents.find((doc) => doc.type === "PASSPORT") ?? document;
        break;
    }

    const licenseCountry = license.issuedPlace?.name?.[locale] || license.issuedPlace?.name?.en || t("notAvailable");

    const licenseExpiryDate = getFullDate(license.expiry, license.hijrahExpiry);

    const phone = mobileNumber ? `+${countryCode} ${mobileNumber}` : t("notAvailable");

    const agreements = bookingCount?.totalAgreementCount;
    const noShows = bookingCount?.totalNoShowCount;
    const companyName = t("notAvailable");

    return (
      <>
        {/* Header Section */}
        <div className="flex items-start justify-between px-6 pb-4 pt-4 shadow">
          <div className="space-y-1">
            <div className="flex gap-2">
              <CopyLink />
              <OpenLink url={`#`} className="flex items-center gap-2" />
            </div>
            <div className="flex items-center gap-2">
              <h2 className="text-2xl font-bold capitalize">{`${firstName} ${lastName}`}</h2>
            </div>
          </div>
        </div>

        <ScrollArea
          className="hide-scrollbar h-full p-0 pb-24
        "
        >
          {/* Contact Information */}
          <div className="mb-6 space-y-4 px-6 pt-6">
            <div className="flex items-center gap-3">
              <Phone className="h-5 w-5" />
              <span>{phone}</span>
            </div>
            <div className="flex items-center gap-3">
              <EnvelopeSimple className="h-5 w-5" />
              <span>{email ?? t("notAvailable")}</span>
            </div>
            {false && (
              <div className="flex items-center gap-3">
                <Buildings className="h-5 w-5" />
                <span>{companyName}</span>
              </div>
            )}
          </div>

          <Separator className="my-6" />

          {/* Statistics */}
          <div className="mb-6 flex gap-6 px-6">
            <div className="flex items-center gap-2">
              <Key className="h-5 w-5 " />
              <span>{t("agreements", { count: agreements ?? 0 })}</span>
            </div>
            <div className="flex items-center gap-2">
              <EyeSlash className="h-5 w-5 " />
              <span>{t("noShows", { count: noShows ?? 0 })}</span>
            </div>
          </div>

          <Separator className="my-6 h-1" />

          {/* Driver Information */}
          <div className="mb-6 space-y-4 px-6">
            <h3 className="text-lg font-semibold">{t("driverInformation")}</h3>

            <div className="space-y-2">
              <p className="text-sm">{t("nationality")}</p>
              <p>{nationality?.name?.[locale] || nationality?.name?.en || t("notAvailable")}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm">{t("idNumber")}</p>
                <p>{document.documentNo ?? t("notAvailable")}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm">{t("idCode")}</p>
                <p>{document.id ?? t("notAvailable")}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm">{t("dateOfBirth")}</p>
                <div>
                  <p>{dob ? `${dob} CE` : t("notAvailable")}</p>
                  <p>{hijrahDob ? `${hijrahDob} AH` : t("notAvailable")}</p>
                </div>
              </div>
              <div className="space-y-2">
                <p className="text-sm">{t("age")}</p>
                <p>{age}</p>
              </div>
            </div>
          </div>

          <Separator className="my-6 h-1" />

          {/* Driver License */}
          <div className="mb-6 space-y-4 px-6">
            <h3 className="text-lg font-semibold">{t("driverLicense")}</h3>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm">{t("origin")}</p>
                <p>{licenseCountry}</p>
              </div>
              <div className="space-y-2">
                <p className="text-sm">{t("licenseNumber")}</p>
                <p>{license.documentNo ?? t("notAvailable")}</p>
              </div>
            </div>

            <div className="space-y-2">
              <p className="text-sm">{t("expiryDate")}</p>
              <p>{licenseExpiryDate.gregorean ? licenseExpiryDate.gregorean + " CE" : t("notAvailable")}</p>
              <p>{licenseExpiryDate.hijri ? licenseExpiryDate.hijri + " AH" : t("notAvailable")}</p>
            </div>
          </div>

          <Separator className="my-6 h-1" />

          {/* Last Bookings */}
          <div className="space-y-4 ">
            <h3 className="px-6 text-lg font-semibold">{t("lastBookings")}</h3>

            {bookings.length ? (
              bookings.map((booking) => {
                const days = differenceInDays(
                  new Date(booking.dropOffDateTime * 1000),
                  new Date(booking.pickupDateTime * 1000)
                );
                const _bookingDate = new Date(booking.bookingDateTime * 1000);
                const bookingDate = format(_bookingDate, "dd-MM-yyyy");
                // get status and remove the underscore if it has and make it formate like this "noShow"
                const status = formatToCamel(booking.status ?? "");
                // @ts-expect-error TODO: Fix this type
                const _status = bookingT(status);

                return (
                  <Fragment key={booking.dropOffDateTime + booking.pickupDateTime}>
                    <div className="space-y-4 px-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center text-base font-bold">
                          <span>{booking.bookingNo ?? t("notAvailable")}</span>
                          <Badge
                            variant="secondary"
                            className={`mx-2 rounded-full px-3 font-normal capitalize ${getBadgeColor(booking?.status ?? "UPCOMING")}`}
                          >
                            {_status ?? t("upcoming")}
                          </Badge>
                        </div>
                        <OpenLinkW booking={booking} />
                      </div>
                      <div className="flex gap-4 text-sm">
                        <div className="flex items-center gap-2">
                          <CalendarMinus className="size-5" />
                          <span>{bookingDate ?? t("notAvailable")}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="size-5" />
                          <span>{days ? t("days", { count: days }) : t("notAvailable")}</span>
                        </div>
                      </div>
                    </div>
                    <Separator className="my-4" />
                  </Fragment>
                );
              })
            ) : (
              <p className="px-6">{t("noBookingInformation")}</p>
            )}
          </div>
        </ScrollArea>
      </>
    );
  } catch {
    return <div>{t("driverNotFound")}</div>;
  }
}
