import { ActionsBar } from "../../_components/actions-bar";
import { WALK_IN_NAV_ITEMS } from "../constants";

import PricingBreakdown from "../_components/pricing-breakdown";
import { api } from "@/api";
import { countries } from "country-data-list";
import { getPricingServerInfo } from "../utils";
import SidesheetWrapper from "../../[bookingId]/_components/sidesheet-wrapper";
import { DriverDetails } from "@/app/(portal)/rental/_components/driver-details/driver-details";

export default async function Page({
  searchParams,
}: {
  searchParams: Promise<Record<string, string | string[] | undefined>>;
}) {
  const _searchParams = await searchParams;

  const quoteId: string | undefined = _searchParams.quoteId as string | undefined;

  const countriesResponse = await api.branch.getCountries({
    requiresAuth: false,
  });

  if (countriesResponse?.status !== 200) {
    throw new Error(`Error: ${countriesResponse.status}`);
  }

  const getFilteredCountries = () => {
    return countries.all
      .filter((country) => {
        return countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`);
      })
      .map((country) => ({
        ...country,
        id: countriesResponse.body.data.find((c) => country.countryCallingCodes[0] === `+${c.code}`)?.id,
      }));
  };

  const filterdCountries = getFilteredCountries();

  const driverName = _searchParams?.driverName as string;
  const driverUid = _searchParams?.driverUid as string;
  const driverMode = _searchParams?.driverMode as string;
  const driverTitle = _searchParams?.driverTitle as string;

  // Driver information
  const driver = {
    title: driverTitle,
    name: driverName,
    driverUId: driverUid,
  };

  const isEnableContinue = !driverName || !driverUid || driverMode === "edit";

  const { quoteResponse } = await getPricingServerInfo({ quoteId });
  return (
    <section className="mb-10 grid grid-cols-12 gap-10">
      <div className="col-span-8 space-y-6">
        <div className="max-w-3xl !p-0">
          <DriverDetails searchParams={_searchParams} countries={filterdCountries} />
        </div>
        <ActionsBar
          bookingNo="1234"
          className="w-full"
          navItemsArray={WALK_IN_NAV_ITEMS}
          successCtaDisabled={isEnableContinue}
        />
      </div>
      <div className="col-span-4">
        <PricingBreakdown
          _searchParams={_searchParams}
          quoteResponse={quoteResponse}
          quoteId={quoteId ? String(quoteId) : undefined}
        >
          {driver.driverUId && <SidesheetWrapper driverUId={driver.driverUId ?? ""} />}
        </PricingBreakdown>
      </div>
    </section>
  );
}
