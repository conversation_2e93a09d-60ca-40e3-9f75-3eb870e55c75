"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useParams, usePathname } from "next/navigation";
import { ProgressBarLink } from "@/components/progress-bar";
import { type Route } from "next";
import { getLastPathSegment, trackEvent } from "@/lib/utils";
import { type AllBooking, type CompletedBooking } from "./types";
import { type Agreement } from "@/api/contracts/booking/schema";
import { useConsolidatedBookingProperties } from "@/lib/hooks/useConsolidatedBookingProperties";

export const AgreementButton = ({
  children,
  rowId,
  buttonTitle,
  booking,
  ...props
}: {
  children: React.ReactNode;
  rowId?: string;
  buttonTitle: string;
  booking: AllBooking | Agreement | CompletedBooking;
  [key: string]: unknown;
}) => {
  const params = useParams();
  const { id: branchId } = params;
  const pathname = usePathname();
  const getEventProperties = useConsolidatedBookingProperties(booking);

  const handleClick = (buttonType: string) => {
    try {
      const currentPage = getLastPathSegment(pathname);
      const branchName = localStorage.getItem("branchName")!;
      const baseProperties = getEventProperties(currentPage);

      const eventProperties =
        buttonType === "endAgreement"
          ? baseProperties
          : {
              ...baseProperties,
              actual_pickup_branch_id: Number(branchId),
              actual_pickup_branch_name: branchName,
              actual_pickup_date: new Date().toISOString(),
            };
      const eventName = buttonType === "endAgreement" ? "End Agreement Clicked" : "Start Agreement Clicked";
      trackEvent(eventName, eventProperties);
    } catch (error) {
      console.log("Error in sending event::", error);
    }
  };

  if (buttonTitle === "endAgreement" && rowId) {
    return (
      <ProgressBarLink href={`/rental/branches/${branchId as string}/close-agreements/${rowId}/inspection-details`}>
        <Button
          variant={"outline"}
          className="h-8 rounded-md p-4 text-sm  transition-colors max-lg:hidden"
          onClick={() => handleClick(buttonTitle)}
          {...props}
        >
          {children}
        </Button>
      </ProgressBarLink>
    );
  }
  return (
    <ProgressBarLink href={`/rental/branches/${branchId as string}/bookings/${rowId}/booking-details` as Route}>
      <Button
        variant={"outline"}
        className="h-8 rounded-md p-4 text-sm transition-colors max-lg:hidden"
        onClick={() => handleClick(buttonTitle)}
        {...props}
      >
        {children}
      </Button>
    </ProgressBarLink>
  );
};
