"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import { format } from "date-fns";
import { ar, enUS } from "date-fns/locale";
import type { Quote<PERSON>rice, <PERSON>donRate, IDriveSubSchema } from "@/api/contracts/booking/schema";
import { useLocale, useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect} from "react";
import { PricingBreakdownSkeleton } from "../../branches/[id]/bookings/_components/skeletons/pricing-breakdown-skeleton";
import { getFromSeconds } from "@/lib/utils";

interface IPickupDetails {
  date: string;
  location: string;
  city: string;
}

interface Iinsurance {
  id: number;
  name: {
    ar?: string | undefined;
    en?: string | undefined;
  };
  description: {
    ar?: string | undefined;
    en?: string | undefined;
  };
  code: string;
  recommended: boolean;
  deductible: number;
  enabled?: boolean | undefined;
}

/**
 * Main PricingBreakdown component that displays pricing information
 * Now fetches its own data rather than receiving it as props
 */
export default function PricingBreakdown({
  quoteResponse,
  children,
}: {
  quoteResponse: QuotePrice;
  children: React.ReactNode;
}) {
  const t = useTranslations("pricing");
  const bookingT = useTranslations("bookings");
  const locale = useLocale() as "en" | "ar";
  const searchParamsObj = useSearchParams();

  const searchParams = Object.fromEntries(searchParamsObj.entries()) as Record<string, string | string[] | undefined>;

  const dateLocale = locale === "ar" ? ar : enUS;

  const priceCalculatorData = mapQuotePriceToCalculatePrice(quoteResponse);
  const originalQuoteData = quoteResponse;
  console.log("Price Calculator Data:", quoteResponse);

  const { rentalAmount, totalAddOnAmount, dropOffAmount, vatPercentage, vatAmount, totalSum, dropOffCharge } =
    priceCalculatorData.priceDetail;
  const { discountDetail, pickupBranch, dropOffBranch,soldDaysInSeconds } = priceCalculatorData;

  // eslint-disable-next-line @typescript-eslint/no-redundant-type-constituents
  const driverDetails: IDriveSubSchema | undefined = priceCalculatorData.driverDetails;
  console.log("Driver Details:", driverDetails);
  useEffect(() => {
    if (!driverDetails) {
      const insuranceAndExtrasElem = document.getElementById("insuranceandextras");
      const additionalDetailsElem = document.getElementById("additionaldetails");
      if (insuranceAndExtrasElem) {
        insuranceAndExtrasElem.style.pointerEvents = "none";
        insuranceAndExtrasElem.style.opacity = "0.5";
      }
      if (additionalDetailsElem) {
        additionalDetailsElem.style.pointerEvents = "none";
        additionalDetailsElem.style.opacity = "0.5";
      }
    } else {
      const insuranceAndExtrasElem = document.getElementById("insuranceandextras");
      const additionalDetailsElem = document.getElementById("additionaldetails");
      if (insuranceAndExtrasElem) {
        insuranceAndExtrasElem.style.pointerEvents = "all";
        insuranceAndExtrasElem.style.opacity = "1";
      }
      if (additionalDetailsElem) {
        additionalDetailsElem.style.pointerEvents = "all";
        additionalDetailsElem.style.opacity = "1";
      }
    }
  }, [driverDetails]);
  const remainingAmount = priceCalculatorData.remainingAmount || 0;

  const b2BPaymentBreakUp = priceCalculatorData.b2BPaymentBreakUp || { payByDriver: 0, payByCompany: 0 };

  const addonIds = searchParams.addOns ?? [];
  const insuranceIds = searchParams.insuranceIds
    ? Array.isArray(searchParams.insuranceIds)
      ? searchParams.insuranceIds.map((id) => Number(id))
      : [Number(searchParams.insuranceIds)]
    : [];

  let pickupTimestamp = quoteResponse?.pickupDateTime || 0;
  let dropOffTimeStamp = quoteResponse?.dropOffDateTime || 0;

  pickupTimestamp = pickupTimestamp ? new Date(pickupTimestamp * 1000).getTime() / 1000 : 0;
  dropOffTimeStamp = dropOffTimeStamp ? new Date(dropOffTimeStamp * 1000).getTime() / 1000 : 0;

  function getPickupDetails(): IPickupDetails {
    const pickupDate = pickupTimestamp
      ? format(pickupTimestamp * 1000, "EEEE, dd/MM/yyyy, HH:mm", { locale: dateLocale })
      : format(new Date(), "EEEE, dd/MM/yyyy, HH:mm", { locale: dateLocale });

    return {
      date: pickupDate,
      location: pickupBranch?.name?.[locale] ?? "",
      city: pickupBranch?.city?.name?.[locale] ?? "",
    };
  }

  const dropOffDate = dropOffTimeStamp
    ? format(dropOffTimeStamp * 1000, "EEEE, dd/MM/yyyy, HH:mm", { locale: dateLocale })
    : "";

const { days, hours: remainingHours } = getFromSeconds(soldDaysInSeconds ?? 0);

  const bookingSummary = t("duration", { days, hours: remainingHours });

  const dropOffDetails = {
    date: dropOffDate,
    location: dropOffBranch?.name?.[locale] ?? "",
    city: dropOffBranch?.city?.name?.[locale] ?? "",
  };

  const allAddonItems = originalQuoteData?.addons || [];
  const addonItems = allAddonItems.filter((addon: AddonRate) =>
    addonIds.length > 0 && addon.id ? addonIds.includes(String(addon.id)) : false
  );
  const allInsuranceItems = originalQuoteData?.insurances || [];

  const driverUId = searchParams?.driverUid as string | undefined;
  const driver = {
    title: driverDetails?.title ?? "Mr",
    name: `${driverDetails?.firstName} ${driverDetails?.lastName}`.trim(),
    driverUId: driverDetails?.driverUId || "",
  };

  if (!quoteResponse?.pickupDateTime) return null;

  return (
    <Card className="w-full h-auto overflow-hidden text-sm text-slate-600">
      {driver.name && driver.driverUId && driverUId && (
        <div className="flex w-full items-center justify-between">
          <CardHeader className="px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CardTitle className="text-lg capitalize text-slate-900">{`${driver?.title}. ${driver?.name}`.toLowerCase()}</CardTitle>
              </div>
            </div>
          </CardHeader>
          <Suspense fallback={<PricingBreakdownSkeleton />}>{children}</Suspense>
        </div>
      )}

      {bookingSummary && (
        <>
          <Separator />
          <div className="flex items-center justify-between p-4">
            <h4 className="text-base font-bold text-slate-900">{bookingT("details.bookingSummary")}</h4>
            <span>{bookingSummary}</span>
          </div>
          <Separator />
        </>
      )}

      <CardContent className="p-0">
        <section className="space-y-4 p-4">
          {getPickupDetails().location && <PickupBranch pickupDetails={getPickupDetails()} />}
          {dropOffDetails.location && (
            <div className="space-y-2">
              <h5 className="font-bold text-slate-900">{bookingT("details.dropOffBranch")}</h5>
              <p className="font-medium text-slate-900">{dropOffDetails.date}</p>
              <p>
                {dropOffDetails.location}, {dropOffDetails.city}
              </p>
            </div>
          )}
        </section>
        <Separator />

        <section>
          <div className="flex items-center justify-between p-4 text-slate-900">
            <h5 className="text-base font-bold">{t("priceBreakdown")}</h5>
            <span>{priceCalculatorData.currency ?? t("SAR")}</span>
          </div>
          <Separator />
          <div className="space-y-2 p-4">
            {rentalAmount && (
              <div className="flex items-center justify-between">
                <span className="overflow-hidden text-ellipsis whitespace-nowrap text-sm leading-relaxed tracking-normal">
                  {t("rentalLabel")}{" "}
                  {t("rentalPeriod", {
                    days,
                    hours: remainingHours,
                  })}
                </span>
                <span>{Number(rentalAmount).toFixed(2)}</span>
              </div>
            )}

            {discountDetail?.discountPercentage && Number(discountDetail.discountPercentage) > 0 && (
              <div className="flex items-center justify-between">
                <span>
                  {t("discount")} {Number(discountDetail.discountPercentage)}%
                  {discountDetail?.promoCode ? ` (${discountDetail.promoCode})` : ""}
                </span>
                <span>-{discountDetail.totalDiscount}</span>
              </div>
            )}

            {insuranceIds?.filter((id: number) => id === 2).length > 0 && originalQuoteData?.cdwOn && (
              <div className="flex items-center justify-between">
                <span>{t("comprehensiveInsurance")}</span>
                <span>{originalQuoteData?.priceDetails?.cdw?.toFixed(2) || 0}</span>
              </div>
            )}

            {allInsuranceItems?.length &&
              insuranceIds?.filter((id: number) => id === 2)?.length === 0 &&
              allInsuranceItems.map((insurance: Iinsurance) => {
                const insuranceName = locale === "ar" ? insurance.name?.ar : insurance.name?.en;
                return (
                  <div className="flex items-center justify-between" key={insurance.id}>
                    <span className="text-sm">{insuranceName}</span>
                    <span className="text-sm">{t("free")}</span>
                  </div>
                );
              })}

            {addonItems && addonItems.length > 0 && (
              <>
                {addonItems.map((addon, index) => {
                  const price =
                    quoteResponse?.priceDetails?.addOnBreakdown?.addOns?.find((item) => item.addOnId === addon.id)
                      ?.price || 0;
                  const addonName = locale === "ar" ? addon.name?.ar : addon.name?.en;
                  return (
                    <div className="flex items-center justify-between" key={addon.id || index}>
                      <span className="text-sm">{addonName || t("addonFallback", { number: index + 1 })}</span>
                      <span>{price.toFixed(2)}</span>
                    </div>
                  );
                })}
              </>
            )}

            {(!addonItems || addonItems.length === 0) && totalAddOnAmount && Number(totalAddOnAmount) > 0 && (
              <div className="flex items-center justify-between">
                <span>{t("addOns")}</span>
                <span>{Number(totalAddOnAmount).toFixed(2)}</span>
              </div>
            )}

            {dropOffCharge !== undefined && Number(dropOffCharge) > 0 && (
              <div className="flex items-center justify-between">
                <span>{t("dropOffCharge")}</span>
                <span>{Number(dropOffCharge).toFixed(2)}</span>
              </div>
            )}

            {dropOffAmount !== undefined && Number(dropOffAmount) > 0 && (
              <div className="flex items-center justify-between">
                <span>{t("dropOffFee")}</span>
                <span>{Number(dropOffAmount).toFixed(2)}</span>
              </div>
            )}

            <div className="flex items-center justify-between">
              <span>
                {t("vat")} {vatPercentage ? `${parseInt(vatPercentage.toString())}%` : ""}
              </span>
              <span>{Number(vatAmount).toFixed(2)}</span>
            </div>
          </div>
        </section>
      </CardContent>

      <CardFooterContent
        totalSum={totalSum ? Number(totalSum) : 0}
        remainingAmount={remainingAmount ? Number(remainingAmount) : 0}
        b2BPaymentBreakUp={b2BPaymentBreakUp}
      />
    </Card>
  );
}

const CardFooterContent = ({
  totalSum,

  remainingAmount,
  b2BPaymentBreakUp,
}: {
  totalSum: number;

  remainingAmount: number;
  b2BPaymentBreakUp: { payByDriver: number; payByCompany: number };
}) => {
  const t = useTranslations("pricing");
  return (
    <CardFooter className="flex flex-col border-t p-0">
      <div className="w-full space-y-3 p-4">
        <div className="flex items-center justify-between text-base font-medium text-slate-900">
          <span>{t("total")}</span>
          <span>{isNaN(totalSum) ? "0.00" : Number(totalSum).toFixed(2)}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          {/* <span>{t("driverPays")}</span> */}
          <span>{t("Driver Pays")}</span>
          <span>
            {isNaN(b2BPaymentBreakUp.payByDriver) ? "0.00" : Number(b2BPaymentBreakUp.payByDriver).toFixed(2)}
          </span>
        </div>
        <div className="flex items-center justify-between text-sm">
          {/* <span>{t("companyPays")}</span> */}
          <span>{t("Company Pays")}</span>
          <span>
            {isNaN(b2BPaymentBreakUp.payByCompany) ? "0.00" : Number(b2BPaymentBreakUp.payByCompany).toFixed(2)}
          </span>
        </div>
      </div>
      <Separator />
    </CardFooter>
  );
};

const PickupBranch = ({ pickupDetails }: { pickupDetails: IPickupDetails }) => {
  const bookingT = useTranslations("bookings");
  return (
    <section className="space-y-4">
      <div className="space-y-2">
        <h5 className="font-bold text-slate-900">{bookingT("details.pickupBranch")}</h5>
        <p className="font-medium text-slate-900">{pickupDetails.date}</p>
        <p>
          {pickupDetails.location}, {pickupDetails.city}
        </p>
      </div>
    </section>
  );
};

const mapQuotePriceToCalculatePrice = (data: QuotePrice) => {
  try {
    const {
      quoteId,
      expiry,
      currency,
      vatPercentage,
      pricePerDay,
      pricePerDayInclVat,
      soldDays,
      soldDaysInSeconds,
      finalPrice,
      authAmount,
      discountPercentage,
      dailyKmsAllowance,
      extraKmsCharge,
      cdwDeductible,
      priceDetails,
      group,
      addons,
    } = data;

    const { rentalSum, vat, extraSum, dropOffSum, discount, dropOffCharge, yaqeenDropOffCharge } = priceDetails || {};

    return {
      quoteId: quoteId || "",
      expiry: expiry || 0,
      currency: currency || "SAR",
      includedComprehensiveInsurance: false,
      addOns: addons || [],
      priceDetail: {
        rentalPerDay: pricePerDay ? String(pricePerDay) : "0",
        rentalPerDayInclVat: pricePerDayInclVat ? String(pricePerDayInclVat) : "0",
        rentalAmount: rentalSum ? String(rentalSum) : "0",
        insuranceAmount: "0",
        totalAddOnAmount: extraSum ? String(extraSum) : "0",
        dropOffAmount: yaqeenDropOffCharge || 0,
        vatPercentage: vatPercentage ? String(vatPercentage) : "15",
        vatAmount: vat ? String(vat) : "0",
        dropOffCharge,
        totalSum: finalPrice ? String(finalPrice ) : "0",
      },
      discountDetail: {
        promoCode: "",
        discountPercentage: discountPercentage ? String(discountPercentage) : "0",
        totalDiscount: discount ? String(discount) : "0",
        discountedPriceDetail: {
          rentalPerDay: pricePerDay ? String(pricePerDay) : "0",
          rentalPerDayInclVat: pricePerDayInclVat ? String(pricePerDayInclVat) : "0",
          rentalAmount: rentalSum ? String(rentalSum) : "0",
          insuranceAmount: "0",
          totalAddOnAmount: extraSum ? String(extraSum) : "0",
          vatPercentage: vatPercentage ? String(vatPercentage) : "15",
          vatAmount: vat ? String(vat) : "0",
          dropOffAmount: yaqeenDropOffCharge || 0,
          totalSum: finalPrice ? String(finalPrice ) : "0",
        },
        isCorporate: false,
      },
      driverDetails: data?.driverDetails ?? undefined,
      vehicleDetail: {
        plateNumber: "",
        vehicleGroupId: group?.id || 0,
        vehicleGroupCode: group?.code || "",
        vehicleMakeId: group?.make?.id ? String(group.make.id) : "",
        vehicleModelId: group?.model?.id ? String(group.model.id) : "",
        isFreeUpgrade: false,
      },
      tariffDetail: {
        insurancePerDay: 0,
        totalInsuranceAmount: 0,
        insuranceDeductible: cdwDeductible ? String(cdwDeductible) : "",
        authorizationAmount: authAmount ? String(authAmount) : "",
        dailyKmsAllowance: dailyKmsAllowance || 0,
        extraKmsCharge: extraKmsCharge ? String(extraKmsCharge) : "",
      },
      totalRentalDurationSeconds: 0,
      soldDays: soldDays || 0,
      soldDaysInSeconds : soldDaysInSeconds || 0,
      allowedLateHours: 0,
      remainingAmount: finalPrice || 0,
      driverPaidAmount: 0,
      b2BPaymentBreakUp: data.b2BPaymentBreakUp || {},
      pickupBranch: data.pickupBranch,
      dropOffBranch: data.dropOffBranch,
      request: {
        pickupBranchId: 0,
        dropOffBranchId: 0,
        pickupDateTime: 0,
        dropOffDateTime: 0,
        insuranceIds: [],
        addOnIds: [],
        promoCode: "",
        vehicleGroupId: 0,
        driverUid: "",
      },
    };
  } catch (error) {
    console.error("Error mapping quote price data:", error);
    return {
      quoteId: "",
      currency: "SAR",
      priceDetail: {
        rentalAmount: "0",
        insuranceAmount: "0",
        totalAddOnAmount: "0",
        dropOffAmount: 0,
        vatPercentage: "15",
        vatAmount: "0",
        dropOffCharge: 0,
        totalSum: "0",
      },
      discountDetail: {},
      remainingAmount: 0,
      driverPaidAmount: 0,
      b2BPaymentBreakUp: {
        payByDriver: 0,
        payByCompany: 0,
      },
    };
  }
};
