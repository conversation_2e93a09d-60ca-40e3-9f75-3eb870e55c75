"use client";
import React, { useState } from "react";
import type { ColumnDef } from "@tanstack/react-table";

import { Checkbox } from "@/components/ui/checkbox";
import { type BlacklistCustomer } from "@/api/contracts/customer-contract";
import { LocalizeText } from "../../rental/_components/localize-text";
import DeleteBlacklistDialog from "./_components/delete-blacklist-dialog";

function BlacklistSwitch({ row }: { row: { original: BlacklistCustomer } }) {
  const [isDeleteBlacklistDialogOpen, setIsDeleteBlacklistDialogOpen] = useState(false);

  return (
    <div className="flex items-center gap-2">
      <Checkbox
        id="unblacklist"
        name="unblacklist"
        defaultChecked={row.original?.deleted}
        onCheckedChange={(checked) => {
          if (checked) {
            setIsDeleteBlacklistDialogOpen(true);
          }
        }}
        aria-label="Unblacklist"
      />
      {isDeleteBlacklistDialogOpen && (
        <DeleteBlacklistDialog
          open={isDeleteBlacklistDialogOpen}
          onOpenChange={setIsDeleteBlacklistDialogOpen}
          deleteId={row.original?.id}
        />
      )}
    </div>
  );
}

export const columns: ColumnDef<BlacklistCustomer>[] = [
  {
    accessorKey: "firstName",
    header: () => <LocalizeText rootKey="blacklist.columns" message="name" />,
    cell: ({ row }) => {
      const { firstName, lastName } = row.original;
      return firstName || lastName ? `${firstName || ""} ${lastName || ""}` : "-";
    },
  },
  {
    accessorKey: "driverCode",
    header: () => <LocalizeText rootKey="blacklist.columns" message="driverCode" />,
    cell: ({ row }) => {
      return row.original?.driverCode ?? "-";
    },
  },

  {
    accessorKey: "mobileNumber",
    header: () => <LocalizeText rootKey="blacklist.columns" message="mobileNumber" />,
    cell: ({ row }) => {
      const { countryCode, mobileNumber } = row.original;
      return mobileNumber ? `${countryCode ? `(${countryCode})-` : ""}${mobileNumber}` : "-";
    },
  },

  {
    accessorKey: "license",
    header: () => <LocalizeText rootKey="blacklist.columns" message="license" />,
    cell: ({ row }) => {
      return row.original?.license ?? "-";
    },
  },
  {
    accessorKey: "idNumber",
    header: () => <LocalizeText rootKey="blacklist.columns" message="idNumber" />,
    cell: ({ row }) => {
      return row.original?.idNumber ?? "-";
    },
  },
  {
    accessorKey: "reason",
    header: () => <LocalizeText rootKey="blacklist.columns" message="reason" />,
    cell: ({ row }) => {
      return row.original?.reason ?? "-";
    },
  },
  {
    accessorKey: "action",
    header: () => <LocalizeText rootKey="blacklist.columns" message="unblacklist" />,
    cell: ({ row }) => {
      return <BlacklistSwitch row={row} />;
    },
  },
];
