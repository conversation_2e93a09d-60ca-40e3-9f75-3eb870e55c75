import { z } from "zod";

const ProjectSchema = z.object({
  projectId: z.string(),
  projectName: z.string(),
});

const paymentCoverages = z.object({
  id: z.number().optional(),
  paymentCoverageId: z.number().optional(),
  available: z.boolean().optional(),
  name: z.string(),
  en: z.string(),
  ar: z.string(),
  active: z.boolean().optional(),
});

const documentSchema = z.object({
  documentNo: z.string().optional(),
  type: z.string().optional(),
  url: z.string().optional(),
  extension: z.string().optional(),
});

const documentsSchema = z.object({
  taxDocument: documentSchema.refine((val) => Object.keys(val).length > 0, {
    message: "Tax document is required",
  }),
  crDocument: documentSchema.refine((val) => Object.keys(val).length > 0, {
    message: "CR document is required",
  }),
  otherDocuments: z.array(documentSchema).optional(),
});

const ServiceSchema = z.object({
  // Contract Detail
  id: z.number().optional(),
  active: z.boolean(),
  contractNumber: z.string().min(1, "Contract Number is required"),
  contractDocument: documentSchema.optional(),
  applyToAllService: z.boolean().optional(),

  // Billing Preferences
  billingCycle: z.string().nonempty("Billing Cycle is required"),
  creditLimit: z.string().nullable().optional(),
  invoiceType: z.string().min(1, "Invoice Type is required"),
  preBillingInAdvance: z.boolean().optional(),
  billEndOfMonth: z.boolean().optional(),

  // Company Payment
  paymentCoverages: z.array(paymentCoverages).optional(),

  // Projects
  projects: z.array(ProjectSchema).optional(),
  createdOn: z.number().optional(),
  updatedOn: z.number().optional(),
});

export const debtorFormSchema = z.object({
  // Personal detail
  name: z
    .string()
    .min(2, {
      message: "Name must be at least 2 characters.",
    })
    .max(150, {
      message: "Name must not be longer than 150 characters.",
    })
    .nonempty("Name is required"),
  nameAr: z
    .string()
    .min(2, {
      message: "Name must be at least 2 characters.",
    })
    .max(150, {
      message: "Name must not be longer than 150 characters.",
    })
    .nonempty("Name is required"),
  debtorGroupId: z.string().nullable().optional(),
  vatNumber: z.string().nonempty("VAT number is required"),
  debtorCode: z.string().nonempty("Debtor code is required"),
  sapCode: z.string().nonempty("SAP code is required"),
  companyType: z.string().nonempty("Company type is required"),
  crNo: z.string().nonempty("Company registration number is required"),

  // Address
  shortAddress: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val) return true; // allow empty (optional)
        return /^[A-Za-z]{4}[0-9]{4}$/.test(val); // validate format if filled
      },
      {
        message: "Short address must be 8 characters: 4 uppercase letters followed by 4 digits",
      }
    ),

  building: z
    .string()
    .nonempty("Building is required")
    .min(2, "Building must be at least 2 digits")
    .max(5, "Building not longer than 5 digits")
    .regex(/^\d+$/, "Building must be numbers only"),
  street: z.string().nonempty("Street is required"),
  secondary: z
    .string()
    .nonempty("Secondary is required")
    .min(2, "Secondary must be at least 2 digits")
    .max(5, "Secondary not longer than 5 digits")
    .regex(/^\d+$/, "Secondary must be numbers only"),
  district: z.string().nonempty("District is required"),
  postalCode: z
    .string()
    .nonempty("Postal code is required")
    .min(5, "Postal code must be at least 5 digits")
    .max(5, "Postal code must be exactly 5 digits")
    .regex(/^\d+$/, "Postal code must be numbers only"),
  city: z.string().nonempty("City is required"),
  cityAr: z.string(),
  countryCode: z.string().optional(),

  // Contact information
  contactPersonName: z
    .string()
    .nonempty("Name is required")
    .min(2, {
      message: "Name must be at least 2 characters.",
    })
    .max(30, {
      message: "Name must not be longer than 15 characters.",
    }),
  emailAddress: z.string().email({
    message: "Please enter a valid email address.",
  }),
  phoneNumber: z
    .string()
    .nonempty("Phone number is required")
    .refine((value) => value.startsWith("+"), {
      message: "Number must start with a '+' sign.",
    })
    .superRefine((value, ctx) => {
      if (value.startsWith("+966") && !/^\+9665\d{8}$/.test(value)) {
        ctx.addIssue({
          code: "custom",
          message: "Valid format: +9665XXXXXXXX (12 digits).",
        });
      } else if (!value.startsWith("+966") && !/^\+\d{7,15}$/.test(value)) {
        ctx.addIssue({
          code: "custom",
          message: "Valid format: +Code with 7-15 digits.",
        });
      }
    }),

  // Debtor Manager
  debtorManager: z.string().nonempty("Debtor manager is required"),

  // Services
  selectedServices: z.array(z.string()).nonempty("Please select atleast one service"),
  services: z.record(ServiceSchema),

  // Documents
  documents: documentsSchema,
}).superRefine((data, ctx) => {
  // Make countryCode required for international companies
  if (data.companyType === "INTERNATIONAL_COMPANY" && !data.countryCode) {
    ctx.addIssue({
      code: "custom",
      path: ["countryCode"],
      message: "Country is required for international companies",
    });
  }
});

export type DebtorFormValues = z.infer<typeof debtorFormSchema>;
export type Document = z.infer<typeof documentSchema>;
