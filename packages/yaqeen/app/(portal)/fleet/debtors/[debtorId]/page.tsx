import { api } from "@/api";

import EditDebtor from "../_components/edit-debtor";

type PageProps = {
  params: Promise<Record<string, string>>;
};

const getData = async (params: Promise<Record<string, string>>) => {
  const { debtorId } = await params;

  const [countries, debtor, debtorGroups, cities, paymentCoverages] = await Promise.allSettled([
    await api.branch.getCountriesWithCode(),
    await api.customer.getDebtorById({
      params: { id: debtorId ?? "" },
    }),
    await api.customer.getDebtorGroup({
      query: { page: 0, pageSize: 1000 },
    }),
    await api.branch.getAllCities(),
    await api.customer.getPaymentCoverage(),
  ]);

  if (countries?.status === "rejected") {
    throw new Error(`Error: ${String(countries?.reason)}`);
  }

  if (countries?.value?.status !== 200) {
    throw new Error(`Error: ${String(countries?.value?.body?.code)}::${String(countries?.value?.body?.desc)}`);
  }

  if (debtor?.status === "rejected") {
    throw new Error(`Error: ${debtor.reason}`);
  }

  if (debtor?.value.status !== 200) {
    throw new Error(`Error: ${debtor.value.body.code}::${debtor.value.body.desc}`);
  }

  if (debtorGroups?.status === "rejected") {
    throw new Error(`Error: ${debtorGroups.reason}`);
  }

  if (debtorGroups?.value.status !== 200) {
    throw new Error(`Error: ${debtorGroups.value.body.code}::${debtorGroups.value.body.desc}`);
  }

  if (cities?.status === "rejected") {
    throw new Error(`Error: ${cities.reason}`);
  }

  if (cities?.value.status !== 200) {
    throw new Error(`Error: ${cities.value.body.code}::${cities.value.body.desc}`);
  }

  if (paymentCoverages?.status === "rejected") {
    throw new Error(`Error: ${paymentCoverages.reason}`);
  }

  if (paymentCoverages?.value.status !== 200) {
    throw new Error(`Error: ${paymentCoverages.value.body.code}::${paymentCoverages.value.body.desc}`);
  }

  return {
    debtor: debtor.value.body,
    cities: cities.value.body,
    debtorGroups: debtorGroups.value.body,
    paymentCoverages: paymentCoverages.value.body,
    countries: countries.value.body,
  };
};

export default async function DebtorCreatePage({ params }: PageProps) {
  const { debtor, cities, debtorGroups, paymentCoverages, countries } = await getData(params);

  return (
    <EditDebtor
      debtor={debtor}
      cities={cities?.data}
      debtorGroups={debtorGroups}
      paymentCoverages={paymentCoverages}
      countries={countries}
    />
  );
}
