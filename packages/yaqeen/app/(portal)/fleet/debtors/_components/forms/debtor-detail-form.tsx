"use client";

import { useTranslations } from "next-intl";
import { useFormContext, useWatch } from "react-hook-form";

import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

import DebtorProfileForm from "./debtor-profile-form";
import AddressForm from "./address-form";
import ContactForm from "./contact-form";
import DebtorManagerForm from "./debtor-manager-form";

import { type BranchCities, type CountryListRes } from "@/api/contracts/branch-contract";
import { type DebtorGroupsRes } from "@/api/contracts/customer-contract";

interface DebtorDetailProps {
  editMode?: boolean;
  cities: BranchCities;
  countries: CountryListRes;
  debtorGroups: DebtorGroupsRes;
}

export default function DebtorDetailForm({ editMode = false, cities, countries, debtorGroups }: DebtorDetailProps) {
  const t = useTranslations("debtors.debtorProfile");
  const { control } = useFormContext();
  const companyType = useWatch({ control, name: "companyType" });

  // Only show countries dropdown for international companies
  const isInternationalCompany = companyType === "INTERNATIONAL_COMPANY";

  return (
    <Card className="mb-10 flex max-w-3xl flex-col">
      <CardHeader className="flex w-full flex-row justify-between gap-2 p-4">
        <CardTitle className="text-lg font-bold">{t("title")}</CardTitle>
      </CardHeader>
      <Separator />
      <CardContent className="p-0">
        <DebtorProfileForm editMode={editMode} debtorGroups={debtorGroups} />
        <Separator />
        <AddressForm cities={cities} countries={isInternationalCompany ? countries : undefined} />
        <Separator />
        <ContactForm />
        <Separator />
        <DebtorManagerForm />
      </CardContent>
    </Card>
  );
}
