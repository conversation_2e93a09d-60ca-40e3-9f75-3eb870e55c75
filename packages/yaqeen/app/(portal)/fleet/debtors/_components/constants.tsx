export const SERVICES = [
  {
    id: "lease",
    label: "Lease",
    value: "LEASE",
  },
  {
    id: "rental",
    label: "Rental",
    value: "RENTAL",
  },
  {
    id: "ucs",
    label: "UCS",
    value: "UCS",
  },
  {
    id: "commercial",
    label: "Commercial",
    value: "COMMERCIAL",
  },
] as const;

export const SERVICES_KEYS = {
  RENTAL: "rental",
  LEASE: "lease",
  UCS: "ucs",
  COMMERCIAL: "commercial",
} as const;

export const DEBTOR_STATUS = [
  {
    label: "Active",
    value: "active",
  },
  {
    label: "Inactive",
    value: "inactive",
  },
];

export type SERVICE_TYPE = "LEASE" | "RENTAL" | "UCS" | "COMMERCIAL";

export interface Option {
  id?: number;
  label: string;
  value: string;
}

export const companyTypeOption = [
  {
    id: 1,
    label: "corporate",
    value: "CORPORATE",
  },
  {
    id: 2,
    label: "government",
    value: "GOVERNMENT",
  },
  {
    id: 3,
    label: "semiGovernment",
    value: "SEMI_GOVERNMENT",
  },
  {
    id: 4,
    label: "interCompany",
    value: "INTER_COMPANY",
  },
  {
    id: 5,
    label: "internationalCompany",
    value: "INTERNATIONAL_COMPANY",
  },
];

export type companyTypeOptionKeys =
  | "corporate"
  | "government"
  | "semiGovernment"
  | "interCompany"
  | "internationalCompany";

export const defaultBillingCycle = "THIRTY_DAYS";
export const billingCycleOption = [
  {
    id: 1,
    label: "30Days",
    value: "THIRTY_DAYS",
  },
  {
    id: 2,
    label: "calendarDate",
    value: "CALENDAR_DAYS",
  },
];

export type billingCycleOptionKeys = "30Days" | "calendarDate";

export const defaultInvoiceType = "CONSOLIDATED";
export const invoiceTypeOption = [
  {
    id: 1,
    label: "separate",
    value: "SEPARATE",
  },
  {
    id: 2,
    label: "consolidated",
    value: "CONSOLIDATED",
  },
  {
    id: 3,
    label: "consolidatedByPO",
    value: "CONSOLIDATED_BY_PO",
  },
];

export type invoiceTypeOptionKeys = "separate" | "consolidated" | "consolidatedByPO";

export const defaultDocumentValue = {
  fileName: "",
  type: "",
  url: "",
  extension: "",
};

export const defaultServiceValue = {
  active: true,
  contractNumber: "",
  billingCycle: defaultBillingCycle,
  creditLimit: "",
  invoiceType: defaultInvoiceType,
  contractDocument: defaultDocumentValue,
  applyToAllService: false,
  preBillingInAdvance: false,
  billEndOfMonth: false,
};

export const ALLOWED_MIME_TYPES = ["application/pdf", "image/jpeg", "image/png", "image/webp"];
export const ALLOWED_EXTENSIONS = ["pdf", "jpg", "jpeg", "png", "webp"];
