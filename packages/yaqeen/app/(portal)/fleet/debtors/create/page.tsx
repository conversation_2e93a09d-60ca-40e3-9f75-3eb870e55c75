import { api } from "@/api";

import { getTranslations } from "next-intl/server";

import CreateDebtor from "../_components/create-debtor";

import PageTitle from "../_components/page-title";

const getData = async () => {
  const [cities, countries, debtorGroups, paymentCoverages] = await Promise.allSettled([
    await api.branch.getAllCities(),
    await api.branch.getCountriesWithCode(),
    await api.customer.getDebtorGroup({
      query: { page: 0, pageSize: 1000 },
      cache: "no-cache",
    }),
    await api.customer.getPaymentCoverage(),
  ]);

  if (cities?.status === "rejected") {
    throw new Error(`Error: ${String(cities?.reason)}`);
  }

  if (cities?.value?.status !== 200) {
    throw new Error(`Error: ${String(cities?.value?.body?.code)}::${String(cities?.value?.body?.desc)}`);
  }

  if (countries?.status === "rejected") {
    throw new Error(`Error: ${String(countries?.reason)}`);
  }

  if (countries?.value?.status !== 200) {
    throw new Error(`Error: ${String(countries?.value?.body?.code)}::${String(countries?.value?.body?.desc)}`);
  }

  if (debtorGroups?.status === "rejected") {
    throw new Error(`Error: ${String(debtorGroups?.reason)}`);
  }

  if (debtorGroups?.value?.status !== 200) {
    throw new Error(`Error: ${String(debtorGroups?.value?.body?.code)}::${String(debtorGroups?.value?.body?.desc)}`);
  }

  if (paymentCoverages?.status === "rejected") {
    throw new Error(`Error: ${String(paymentCoverages?.reason)}`);
  }

  if (paymentCoverages?.value?.status !== 200) {
    throw new Error(
      `Error: ${String(paymentCoverages?.value?.body?.code)}::${String(paymentCoverages?.value?.body?.desc)}`
    );
  }

  return {
    cities: cities.value.body,
    countries: countries.value.body,
    debtorGroups: debtorGroups.value.body,
    paymentCoverages: paymentCoverages.value.body,
  };
};

export default async function DebtorCreatePage() {
  const { cities, countries, debtorGroups, paymentCoverages } = await getData();

  const t = await getTranslations("debtors.createDebtor");

  return (
    <>
      <PageTitle title={t("title")} description={t("desc")} />
      <div className="p-6">
        <CreateDebtor
          cities={cities?.data}
          countries={countries}
          debtorGroups={debtorGroups}
          paymentCoverages={paymentCoverages}
        />
      </div>
    </>
  );
}
