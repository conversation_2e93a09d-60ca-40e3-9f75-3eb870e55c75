"use client";

import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { useLocale } from "next-intl";

import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/hooks/use-toast";
import { updateMake } from "@/lib/actions";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

interface EditMakeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultValues: {
    id: number;
    englishName: string;
    arabicName: string;
  };
}

function SubmitButton({ confirmed }: { confirmed: boolean }) {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button type="submit" disabled={pending || !confirmed}>
      {pending ? t("saveLoading") : t("saveCTA")}
    </Button>
  );
}

export function EditMakeDialog({ open, onOpenChange, defaultValues }: EditMakeDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [confirmed, setConfirmed] = useState(false);
  const t = useTranslations("fleetManagement");

  const locale = useLocale() as "en" | "ar";

  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(updateMake, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: t("successMessages.title"),
        description: t("successMessages.makeUpdated"),
        variant: "success",
      });
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: t("error"),
        description: state.message,
        variant: "destructive",
      });
    }
  }, [state, toast, router, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="sm:max-w-[425px]"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        <DialogHeader>
          <DialogTitle>{t("make.edit.title")}</DialogTitle>
        </DialogHeader>
        <form action={formAction}>
          <input type="hidden" name="makeId" value={defaultValues.id} />
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label dir="ltr" htmlFor="makeEnglishName">
                Make English Name
              </Label>
              <Input
                id="makeEnglishName"
                name="englishName"
                defaultValue={defaultValues.englishName}
                placeholder="Enter make name in English"
                hasError={!!state?.errors?.englishName}
                dir="ltr"
                required
              />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label className="font-arabic text-right" htmlFor="makeArabicName">
                إسم نوع السيارة بالعربية
              </Label>
              <Input
                id="makeArabicName"
                name="arabicName"
                defaultValue={defaultValues.arabicName}
                placeholder="ضع اسم النوع"
                dir="rtl"
                hasError={!!state?.errors?.arabicName}
                className="font-arabic"
                required
              />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
            <div className={`flex space-x-2 ${locale === "ar" ? " space-x-reverse" : ""}`}>
              <Checkbox
                className="mt-1"
                id="confirmed"
                name="confirmed"
                checked={confirmed}
                onCheckedChange={() => setConfirmed(!confirmed)}
              />
              <Label htmlFor="confirmed" className="text-sm text-slate-700">
                {t("make.edit.confirmation")}
              </Label>
            </div>
          </div>
          <div className="mt-6 flex justify-end gap-3">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              {t("cancelCTA")}
            </Button>
            <SubmitButton confirmed={confirmed} />
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
