"use client";
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CalendarPlusIcon } from "@phosphor-icons/react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import { useActionState } from "react";
import { createModel } from "@/lib/actions";
import { useToast } from "@/lib/hooks/use-toast";
import { useFormStatus } from "react-dom";
import { useRouter } from "next/navigation";
import { useCustomQuery } from "@/lib/hooks/use-query";
import { useLocale, useTranslations } from "next-intl";
import { type Make } from "@/api/contracts/fleet/make/make-contract";
import { type Category } from "@/api/contracts/fleet/categories/category-contract";
import { type VehicleGroup } from "@/api/contracts/rental/availability-contract";
import { Loader2 } from "lucide-react";
import SearchableSelect, { type Option as SearchableOption } from "@/components/ui/searchable-select";

type Option = SearchableOption;

interface CreateModelFormProps {
  makes: Option[];
  categories: Option[];
  groups: Option[];
  onCancel: () => void;
}

type State = {
  message: string | null;
  errors: {
    make?: string;
    modelEnglishName?: string;
    modelArabicName?: string;
    vehicleGroup?: string;
    category?: string;
    versionName?: string;
    sapMaterialId?: string;
  };
  success: boolean;
  modelId?: number | string;
};

function SubmitButton() {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button type="submit" disabled={pending}>
      {pending ? t("models.createModel.createLoading") : t("models.createModel.createModelCTA")}
    </Button>
  );
}

function CreateModelForm({ makes, categories, groups, onCancel }: CreateModelFormProps) {
  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };
  const { toast } = useToast();
  const router = useRouter();
  const t = useTranslations("fleetManagement");
  const [state, formAction] = useActionState(createModel, initialState);
  const [formData, setFormData] = useState({
    makeId: "",
    modelEnglishName: "",
    modelArabicName: "",
    vehicleClassId: "",
    vehicleGroup: "",
    modelVersion: "",
    sapMaterialId: "",
  });

  useEffect(() => {
    if (state?.success) {
      toast({
        title:  t("successMessages.title"),
        description: t("successMessages.modelCreated"),
        variant: "success",
        duration: 3000,
      });
      onCancel();
      router.push(`/fleet/vehicles/models/${state.modelId}`);
    } else if (state?.message) {
      toast({
        title: t("error"),
        description: state.message,
        variant: "destructive",
        duration: 3000,
      });
    }
  }, [state, toast, onCancel, router]);

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return (
    <form className="px-4" action={formAction}>
      <div className="flex flex-col space-y-4 py-4">
        {/* Vehicle Make */}
        <div className="flex flex-col space-y-2">
          <Label htmlFor="make">{t("models.createModel.vehicleMake")}</Label>
          <SearchableSelect
            options={makes}
            value={formData.makeId}
            onValueChange={(option) => handleInputChange("makeId", typeof option === "string" ? option : option.value)}
            placeholder={t("models.createModel.vehicleMakePlaceholder")}
            searchPlaceholder={t("models.createModel.vehicleMakePlaceholder")}
          />
          <input type="hidden" name="make" value={formData.makeId} />
          {state?.errors?.make && <span className="text-sm text-red-500">{state.errors.make}</span>}
        </div>
        {/* Model English Name */}
        <div className="flex flex-col space-y-2">
          <Label dir="ltr" htmlFor="modelEnglishName">Model English Name</Label>
          <Input
            dir="ltr"
            id="modelEnglishName"
            name="modelEnglishName"
            placeholder="Place model name"
            hasError={!!state?.errors?.modelEnglishName}
            value={formData.modelEnglishName}
            onChange={(e) => handleInputChange("modelEnglishName", e.target.value)}
            required
          />
          {state?.errors?.modelEnglishName && (
            <span className="text-sm text-red-500">{state.errors.modelEnglishName}</span>
          )}
        </div>
        {/* Model Arabic Name */}
        <div className="flex flex-col space-y-2">
          <Label dir="ltr" className="font-arabic text-right" htmlFor="modelArabicName">
            إسم نوع السيارة بالعربية
          </Label>
          <Input
            id="modelArabicName"
            name="modelArabicName"
            placeholder="ضع اسم النوع"
            dir="rtl"
            className="font-arabic"
            hasError={!!state?.errors?.modelArabicName}
            value={formData.modelArabicName}
            onChange={(e) => handleInputChange("modelArabicName", e.target.value)}
            required
          />
          {state?.errors?.modelArabicName && (
            <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.modelArabicName}</span>
          )}
        </div>
        {/* Group Name */}
        <div className="flex flex-col space-y-2">
          <Label htmlFor="vehicleGroup">{t("models.createModel.vehicleGroup")}</Label>
          <SearchableSelect
            options={groups}
            value={formData.vehicleGroup}
            onValueChange={(option) => handleInputChange("vehicleGroup", typeof option === "string" ? option : option.value)}
            placeholder={t("models.createModel.vehicleGroupPlaceholder")}
            searchPlaceholder={t("models.createModel.vehicleGroupPlaceholder")}
          />
          <input type="hidden" name="vehicleGroup" value={formData.vehicleGroup} />
          {state?.errors?.vehicleGroup && <span className="text-sm text-red-500">{state.errors.vehicleGroup}</span>}
        </div>
        {/* Vehicle Category */}
        <div className="flex flex-col space-y-2">
          <Label htmlFor="category">{t("models.createModel.vehicleCategory")}</Label>
          <SearchableSelect
            options={categories}
            value={formData.vehicleClassId}
            onValueChange={(option) => handleInputChange("vehicleClassId", typeof option === "string" ? option : option.value)}
            placeholder={t("models.createModel.vehicleCategoryPlaceholder")}
            searchPlaceholder={t("models.createModel.vehicleCategoryPlaceholder")}
            clearable={false}
            noResultsText={t("models.createModel.noCategoriesFound")}
            maxHeight="160px"
          />
          <input type="hidden" name="category" value={formData.vehicleClassId} />
          {state?.errors?.category && <span className="text-sm text-red-500">{state.errors.category}</span>}
        </div>
        {/* Version English Name */}
        <div className="flex flex-col space-y-2">
          <Label dir="ltr" htmlFor="versionName">Version English Name</Label>
          <Input
            dir="ltr"
            id="versionName"
            name="versionName"
            placeholder="Place version name"
            hasError={!!state?.errors?.versionName}
            value={formData.modelVersion}
            onChange={(e) => handleInputChange("modelVersion", e.target.value)}
            required
          />
          {state?.errors?.versionName && <span className="text-sm text-red-500">{state.errors.versionName}</span>}
        </div>
        {/* SAP material ID */}
        <div className="flex flex-col space-y-2">
          <Label htmlFor="sapMaterialId">{t("models.createModel.sapMaterialId")}</Label>
          <Input
            id="sapMaterialId"
            name="sapMaterialId"
            placeholder={t("models.createModel.sapMaterialIdPlaceholder")}
            hasError={!!state?.errors?.sapMaterialId}
            value={formData.sapMaterialId}
            onChange={(e) => handleInputChange("sapMaterialId", e.target.value)}
            required
          />
          {state?.errors?.sapMaterialId && <span className="text-sm text-red-500">{state.errors.sapMaterialId}</span>}
        </div>
      </div>
      {state?.message && <div className="mb-4 text-sm text-red-500">{state.message}</div>}
      <DialogFooter className="gap-3 sm:space-x-0">
        <Button variant="outline" onClick={onCancel} type="button">
          {t("cancelCTA")}
        </Button>
        <SubmitButton />
      </DialogFooter>
    </form>
  );
}

export default function CreateModelDialog() {
  const [open, setOpen] = useState(false);
  const locale = useLocale() as "en" | "ar";  
  const t = useTranslations("fleetManagement");
  const { data, isLoading, error } = useCustomQuery<{
    makes: Make[];
    categories: Category[];
    groups: VehicleGroup[];
  }>(["model-create-options"], "/next-api/fleet/models");

  let makes: Option[] = [];
  let categories: Option[] = [];
  let groups: Option[] = [];

  if (data) {
    makes = (data.makes || []).map((make) => ({
      value: String(make.id),
      label: make.name?.[locale] || make.name?.en,
    }));
    categories = (data.categories || []).map((cat) => ({
      value: String(cat.id),
      label: cat.name?.[locale] || cat.name?.en,
    }));
    groups = (data.groups || []).map((group) => ({
      value: String(group.code),
      label: group.code,
    }));
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" className="flex items-center gap-2 rounded-md">
          <CalendarPlusIcon className="size-4" />
          {t("models.createModel.title")}
        </Button>
      </DialogTrigger>
      <DialogContent className="px-0 sm:max-w-[425px]">
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("models.createModel.title")}</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex h-[30rem] w-auto">
            <Loader2 className="m-auto me-2 h-12 w-[100%] animate-spin" />
          </div>
        ) : error ? (
          <div className="p-4 text-red-500">{t("models.createModel.loadingError")}</div>
        ) : (
          open && (
            <CreateModelForm makes={makes} categories={categories} groups={groups} onCancel={() => setOpen(false)} />
          )
        )}
      </DialogContent>
    </Dialog>
  );
}
