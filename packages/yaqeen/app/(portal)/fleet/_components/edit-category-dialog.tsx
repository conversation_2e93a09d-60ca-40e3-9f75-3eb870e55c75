"use client";

import { updateCategory } from "@/lib/actions";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/lib/hooks/use-toast";
import { useRouter } from "next/navigation";
import { useActionState, useEffect, useState } from "react";
import { useFormStatus } from "react-dom";
import { useLocale, useTranslations } from "next-intl";

type State = {
  message: string | null;
  errors: {
    englishName?: string;
    arabicName?: string;
  };
  success: boolean;
};

interface EditCategoryDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultValues: {
    id: number;
    englishName: string;
    arabicName: string;
  };
}

function SubmitButton({ confirmed }: { confirmed: boolean }) {
  const { pending } = useFormStatus();
  const t = useTranslations("fleetManagement");

  return (
    <Button type="submit" disabled={pending || !confirmed}>
      {pending ? t("categories.edit.saving") : t("categories.edit.saveChanges")}
    </Button>
  );
}

export function EditCategoryDialog({ open, onOpenChange, defaultValues }: EditCategoryDialogProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [confirmed, setConfirmed] = useState(false);
  const t = useTranslations("fleetManagement");
  const locale = useLocale() as "en" | "ar";
  const initialState: State = {
    message: null,
    errors: {},
    success: false,
  };

  const [state, formAction] = useActionState(updateCategory, initialState);

  useEffect(() => {
    if (state?.success) {
      toast({
        title: t("successMessages.title"),
        description: t("successMessages.categoryUpdated"),
        variant: "success",
      });
      onOpenChange(false);
    } else if (state?.message) {
      toast({
        title: t("error"),
        description: state.message,
        variant: "destructive",
      });
    }
  }, [state, toast, router, onOpenChange]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="px-0 sm:max-w-[425px]"
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(e) => e.stopPropagation()}
      >
        <DialogHeader className="border-b border-gray-200 px-4 pb-4">
          <DialogTitle>{t("categories.edit.title")}</DialogTitle>
        </DialogHeader>
        <form className="px-4" action={formAction}>
          <input type="hidden" name="categoryId" value={defaultValues.id} />
          <div className="flex flex-col space-y-4 py-4">
            <div className="flex flex-col space-y-2">
              <Label dir="ltr" htmlFor="categoryEnglishName">Category English Name</Label>
              <Input
                id="categoryEnglishName"
                name="englishName"
                defaultValue={defaultValues.englishName}
                placeholder="Enter category name in English"
                hasError={!!state?.errors?.englishName}
                dir="ltr"
                required
              />
              {state?.errors?.englishName && <span className="text-sm text-red-500">{state.errors.englishName}</span>}
            </div>
            <div className="flex flex-col space-y-2">
              <Label dir="ltr" className="font-arabic text-right" htmlFor="categoryArabicName">
                إسم الفئة بالعربية
              </Label>
              <Input
                id="categoryArabicName"
                name="arabicName"
                defaultValue={defaultValues.arabicName}
                placeholder="ضع اسم الفئة"
                dir="rtl"
                hasError={!!state?.errors?.arabicName}
                required
                className="font-arabic"
              />
              {state?.errors?.arabicName && (
                <span className="font-arabic w-full text-right text-sm text-red-500">{state.errors.arabicName}</span>
              )}
            </div>
            <div className={`flex space-x-2 ${locale === "ar" ? " space-x-reverse" : ""}`}>
              <Checkbox
                className="mt-1"
                id="confirmed"
                name="confirmed"
                checked={confirmed}
                onCheckedChange={() => setConfirmed(!confirmed)}
              />
              <Label htmlFor="confirmed" className="text-sm text-slate-700">
                {t("categories.edit.confirmation")}
              </Label>
            </div>
          </div>
          <div className="mt-6 flex justify-end gap-3">
            <Button variant="outline" onClick={() => onOpenChange(false)} type="button">
              {t("cancelCTA")}
            </Button>
            <SubmitButton confirmed={confirmed} />
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
