/* eslint-disable @next/next/no-sync-scripts */
import ProgressBar from "nextjs-toploader";
import { Toaster } from "@/components/ui/toaster";
import { TanstackQueryProvider } from "@/components/providers/query-client-provider";
import { NextIntlClientProvider } from "next-intl";
import { getLocale, getMessages } from "next-intl/server";
import { Inter } from "next/font/google";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { type ReactNode } from "react";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  robots: {
    index: false,
    follow: false,
  },
};

export default async function RootLayout({ children }: { children: ReactNode }) {
  const locale = await getLocale();
  const messages = await getMessages();

  return (
    <html dir={locale === "ar" ? "rtl" : "ltr"} lang={locale}>
      <body className={inter.className}>
        <NextIntlClientProvider messages={messages} locale={locale}>
          <TanstackQueryProvider>
            <NuqsAdapter>
              <ProgressBar color="#93d500" shadow="0 0 10px #2299DD,0 0 5px #2299DD" showSpinner={false} />
              <main className="mb-5">{children}</main>
              <Toaster />
            </NuqsAdapter>
          </TanstackQueryProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
