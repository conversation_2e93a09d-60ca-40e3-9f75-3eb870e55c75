import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const branchId = url.searchParams.get("branchId");

    if (!branchId) {
      return NextResponse.json({ error: "Branch ID is required" }, { status: 400 });
    }

    const response = await api.branch.getAllPos({
      params: {
        id: String(branchId),
      },
    });

    if (response.status === 200) {
      return NextResponse.json(response.body);
    }

    return NextResponse.json({ error: "Failed to fetch POS data" }, { status: response.status });
  } catch (error) {
    console.error("Error fetching POS data:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
