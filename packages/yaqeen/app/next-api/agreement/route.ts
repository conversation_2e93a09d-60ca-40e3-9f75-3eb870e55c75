import { type NextRequest, NextResponse } from "next/server";
import { api } from "@/api";

export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const agreement_id = url.searchParams.get("agreement_id") || "";

    const agreementResponse = await api.booking.getAgreement({
      params: {
        agreementNo: agreement_id,
      },
    });

    if (agreementResponse.status === 200) {
      return NextResponse.json(agreementResponse.body);
    }

    return NextResponse.json({ error: "Failed to fetch agreement details" }, { status: agreementResponse.status });
  } catch (error) {
    console.error("Error fetching agreement details:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
