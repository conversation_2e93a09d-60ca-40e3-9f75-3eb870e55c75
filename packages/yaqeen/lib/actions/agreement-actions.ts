"use server";

import { api } from "@/api";
import { type IReplaceVehicle } from "@/api/contracts/booking/booking-contract";
import { type IDriverFormValues } from "@/app/(portal)/rental/branches/[id]/bookings/[bookingId]/driver-details/types";
import { type PriceCalculationBody } from "@/app/(portal)/rental/branches/[id]/bookings/_components/types";
import { revalidatePath, revalidateTag } from "next/cache";

export async function updateBookingAction(
  bookingId: number,
  quoteId: string,
  vehicleUpgradeReason?: {
    reason: string;
    identifier: string;
    reasonText?: string;
  },
  reward?: {
    provider: "NONE" | "ALFURSAN" | "MOKAFA" | "QITAF";
    customerIdentifier: string;
  }
) {
  try {
    const response = await api.bookingDetails.updateBooking({
      params: {
        bookingId,
      },
      body: {
        newQuoteReference: quoteId ? String(quoteId) : "",
        vehicleUpgradeReason,
        reward,
      },
    });

    return response;
  } catch (error) {
    console.error("Error updating booking:", error);
    throw error;
  }
}

export async function calculatePriceAction({
  bookingId,
  dropOffBranchId,
  dropOffTime,
}: {
  bookingId: string;
  dropOffBranchId: number;
  dropOffTime: number;
}) {
  try {
    const response = await api.bookingDetails.calculatePrice({
      params: {
        bookingId,
      },
      body: {
        dropOffBranchId: Number(dropOffBranchId),
        dropOffTime,
      },
    });

    if ("code" in response.body) {
      return {
        error: response.body,
      };
    }

    return {
      data: response.body,
    };
  } catch (error) {
    console.error("Error calculating price:", error);
    return {
      error: {
        code: "UNKNOWN_ERROR",
        desc: "An unexpected error occurred while calculating the price",
      },
    };
  }
}
export async function calculatePriceActionWalkin(body: PriceCalculationBody) {
  try {
    const response = await api.pricing.calculatorContract.calculatePrice({
      body: body,
    });

    if ("code" in response) {
      return {
        error: response,
      };
    }
    return response;
  } catch (error) {
    console.error("Error calculating price:", error);
    return {
      error: {
        code: "UNKNOWN_ERROR",
        desc: "An unexpected error occurred while calculating waliin the price",
      },
    };
  }
}

export const initiateTajeer = async (bookingNo: string, existingContractNumber?: string) => {
  try {
    revalidateTag(`initiate-tajeer-${bookingNo}`);
    const body = existingContractNumber ? { existingContractNumber } : {};
    const response = await api.tajeer.initiateTajeer({
      cacheTag: `initiate-tajeer-${bookingNo}`,
      params: {
        bookingNo,
      },
      body,
    });
    return response;
  } catch (error) {
    console.error("Error initiating Tajeer:", error);
    throw error;
  }
};

export const validateTajeer = async (bookingNo: string, code: string) => {
  try {
    revalidateTag(`confirm-tajeer-${bookingNo}`);
    const response = await api.tajeer.confirmTajeer({
      cacheTag: `confirm-tajeer-${bookingNo}`,

      params: {
        bookingNo: bookingNo,
      },
      body: {
        otp: code,
      },
    });
    return response;
  } catch (error) {
    console.error("Error initiating Tajeer:", error);
    throw error;
  }
};

export const validateTamm = async (bookingNo: string, code: string) => {
  try {
    revalidateTag(`confirm-tajeer-${bookingNo}`);
    const response = await api.tajeer.confirmTamm({
      cacheTag: `confirm-tajeer-${bookingNo}`,

      params: {
        bookingNo: bookingNo,
      },
      body: {
        otp: code,
      },
    });
    return response;
  } catch (error) {
    console.error("Error initiating Tajeer:", error);
    throw error;
  }
};

export const initiateTamm = async (bookingNo: string) => {
  try {
    revalidateTag(`initiate-tamm-${bookingNo}`);
    const response = await api.tajeer.initiateTamm({
      cacheTag: `initiate-tamm-${bookingNo}`,

      params: {
        bookingNo: bookingNo,
      },
      body: {},
    });
    return response;
  } catch (error) {
    console.error("Error initiating tamm:", error);
    throw error;
  }
};

export const convertToAgreement = async (bookingNo: string) => {
  return await api.tajeer.convertToAgreement({
    body: {
      bookingNo,
    },
  });
};

export const resendOtp = async (bookingNo: string) => {
  try {
    revalidateTag(`resend-otp-${bookingNo}`);
    const response = await api.tajeer.resendOTP({
      cacheTag: `resend-otp-${bookingNo}`,
      params: {
        bookingNo,
      },
      body: null,
    });
    return response;
  } catch (error) {
    console.error("Error resending OTP:", error);
    throw error;
  }
};

export const doPayment = async ({
  bookingId,
  amount,
  branchId,
  paidThrough,
  initiatedFor,
  posId,
  approvalCode,
  last4Digit,
  pathname,
}: {
  bookingId: string;
  amount: string;
  branchId: string;
  paidThrough: "CASH" | "POS";
  initiatedFor: "SECURITY_DEPOSIT_AUTHORIZATION" | "BOOKING";
  posId: string;
  approvalCode: string;
  last4Digit: string;
  pathname: string;
}) => {
  const payment = api.payment.acceptPayment({
    params: {
      bookingId,
    },
    body: {
      amount: Number(amount),
      branchId: Number(branchId),
      paidThrough,
      initiatedFor,
      posDetail: {
        posId,
        approvalCode,
        last4Digit,
      },
    },
  });
  revalidatePath(pathname);
  return payment;
};

export async function closeAgreement(agreementNo: string, quoteId: string, nextVehicleStatus: string) {
  const response = await api.booking.closeAgreement({
    params: {
      agreementNo,
    },
    body: {
      nextVehicleStatus: nextVehicleStatus || "READY",
      quoteId,
    },
  });

  if (response.status !== 200) {
    return response;
  }

  revalidatePath("/(portal)/rental/branches/[id]/close-agreements/[agreementNo]/authorization");
  return response;
}

export async function initiateAgreementExtension(agreementNo: string, quoteId: string) {
  const response = await api.bookingDetails.initiateAgreementExtension({
    params: {
      agreementNo,
    },
    body: {
      quoteId,
    },
  });
  return response;
}

export async function updateDriver(data: IDriverFormValues) {
  const response = await api.driverDetails.updateDriver({
    params: {
      driverUid: String(data.driverUid),
    },
    body: data,
    cache: "no-cache",
  });

  if (response.status !== 200) {
    return response;
  }

  revalidatePath("/(portal)/rental/branches/[id]/bookings/[bookingId]/driver-details");

  return response;
}
export async function driverAttachOrCreate(data: IDriverFormValues, quoteId: string, url?: string) {
  const response = await api.driverDetails.driverAttachOrCreate({
    params: {
      quoteId,
    },
    body: data,
    cache: "no-cache",
  });

  if (response.status !== 200) {
    return response;
  }

  if (url) {
    // redirect(url);
  }
  return response;
}

// Define the penalty data type based on the contract schema
type PenaltyData = {
  penaltyDescription: string;
  penaltyAmount?: string;
  details: {
    type: "DAMAGE" | "TRAFFIC_FINE";
    policeReportUrl?: string;
    severity?: string;
  };
};

export async function addPenaltyAction(agreementVehicleId: number, penaltyData: PenaltyData) {
  try {
    const response = await api.bookingDetails.addVehiclePenalty({
      params: {
        agreementVehicleId,
      },
      body: {
        penaltyAmount: penaltyData.penaltyAmount || "0.00",
        penaltyDescription: penaltyData.penaltyDescription,
        details: {
          type: penaltyData.details.type,
          policeReportUrl: penaltyData.details.policeReportUrl,
          severity: penaltyData.details.severity,
        },
      },
    });

    // Revalidate cached data related to penalties
    revalidatePath(`/rental/close-agreements/[agreementNo]/inspection-details`);

    return response;
  } catch (error) {
    console.error("Error adding vehicle penalty:", error);
    throw error;
  }
}

export async function getVehiclePenalties(agreementVehicleId: number) {
  try {
    const response = await api.bookingDetails.getVehiclePenalties({
      params: {
        agreementVehicleId,
      },
    });

    return response;
  } catch (error) {
    console.error("Error fetching vehicle penalties:", error);
    throw error;
  }
}

export async function updatePenaltyAction(
  agreementVehicleId: number,
  penaltyId: number,
  penaltyData: {
    penaltyAmount: string;
    penaltyDescription: string;
    details: {
      type: "DAMAGE" | "TRAFFIC_FINE";
      policeReportUrl?: string;
      severity?: string;
    };
  }
) {
  try {
    const response = await api.bookingDetails.updateVehiclePenalty({
      params: {
        agreementVehicleId,
        penaltyId,
      },
      body: penaltyData,
    });

    revalidatePath(`/rental/close-agreements/[agreementNo]/inspection-details`);

    return response;
  } catch (error) {
    console.error("Error updating vehicle penalty:", error);
    throw error;
  }
}

export async function deletePenaltyAction(agreementVehicleId: number, penaltyId: number) {
  try {
    const response = await api.bookingDetails.deleteVehiclePenalty({
      params: {
        agreementVehicleId,
        penaltyId,
      },
    });

    revalidatePath(`/rental/close-agreements/[agreementNo]/inspection-details`);

    return response;
  } catch (error) {
    console.error("Error deleting vehicle penalty:", error);
    throw error;
  }
}

export async function addSecurityDepositAction(
  bookingNo: string,
  bookingId: number,
  depositData: {
    depositAmount: string;
    cardType: "CREDIT_CARD" | "DEBIT_CARD";
    entryType: "MANUAL" | "POS";
    approvalCode: string;
    cardLast4Digit: string;
    posMachine: string;
    paidThrough: string;
    branchId: string;
  }
) {
  try {
    revalidateTag(`security-deposit-${bookingNo}`);
    const response = await api.tajeer.addSecurityDeposit({
      cacheTag: `security-deposit-${bookingNo}`,
      params: {
        bookingNo,
      },
      body: depositData,
    });
    revalidatePath(`/rental/branches/${depositData.branchId}/bookings/${bookingId}/authorization`);
    return response;
  } catch (error) {
    console.error("Error adding security deposit:", error);
    throw error;
  }
}
/**
 *
 *
 *
 *  Walk-in flow
 *
 *
 */

export async function searchDrivers(search: string) {
  const response = await api.driverDetails.searchDrivers({
    query: {
      query: search,
      order: "desc",
    },
  });

  return response;
}

export async function getDriverById(driverUid: string) {
  const response = await api.driverDetails.getDriverById({
    query: {
      driverUid: driverUid,
    },
  });

  return response;
}

// createBookingByPayAtBranch
export async function createBookingByPayAtBranch(quoteId: string, paymentType: string) {
  const response = await api.bookingDetails.createBookingByPayAtBranch({
    params: {
      quoteId,
    },
    body: {
      paymentType,
    },
  });

  return response;
}

export async function fetchInvoiceStatus(agreement_number: string, external_id: string) {
  const response = await api.invoiceStatus.fetchInvoiceStatus({
    params: {
      agreement_number,
      external_id,
    },
  });

  return response;
}

export async function fetchInvoiceStatusV2(invoiceNumber: string) {
  const response = await api.invoiceStatus.fetchInvoiceStatusV2({
    params: {
      invoiceNumber,
    },
  });

  return response;
}

export async function fetchBookingInvoices(bookingNumbers: string) {
  const response = await api.invoiceStatus.fetchBookingInvoices({
    query: {
      bookingNumbers,
    },
  });

  return response;
}

export async function generateInvoice(bookingNo: string, configIdentifier = "DRIVER_INVOICE") {
  const response = await api.invoiceStatus.createInvoice({
    body: { bookingNo, configIdentifier },
  });

  return response;
}

export async function retryInvocieZatca(invoiceNumber: string, configIdentifier = "DRIVER_INVOICE") {
  const response = await api.invoiceStatus.retryInvoice({
    params: { invoiceNumber },
    body: { configIdentifier },
  });

  return response;
}

export async function revalidateClientPath(path: string) {
  revalidatePath(path);
}

export async function createInspection({
  agreementNo,
  agreementVehicleId,
  odometerReading,
  inspectionType,
  fuelLevel,
  branchId,
  replacement,
  vehicleStatus,
}: {
  agreementNo: string;
  agreementVehicleId: number;
  odometerReading?: number;
  inspectionType: number;
  fuelLevel?: number;
  branchId: number | string;
  replacement?: boolean;
  vehicleStatus?: string;
}) {
  console.log(`\n HERE IS replacement create:: ${replacement} \n`);
  const currentRoute = replacement ? `/rental/branches/${branchId}/replace-vehicle/${agreementNo}/inspection-details` : `/rental/branches/${branchId}/close-agreements/${agreementNo}/inspection-details`;
  
  const metadata: {
    odometerReading?: number;
    fuelLevel?: number;
    remark?: string;
    replacement?: boolean;
    vehicleStatus?: string;
  } = {};
  if (odometerReading) {
    metadata.odometerReading = odometerReading;
  }
  if (fuelLevel !== undefined && fuelLevel !== null) {
    metadata.fuelLevel = fuelLevel;
  }
  if (replacement) {
    metadata.replacement = replacement;
  }
  if (vehicleStatus) {
    metadata.vehicleStatus = vehicleStatus;
  }

  const body = {
      inspectionType, // checkin type
      metadata,
      branchId: Number(branchId),
    };

  console.log(`\n HERE IS body CREATE INSPECTION:: ${JSON.stringify(body)} \n`);
  const response = await api.booking.submitVehicleInspection({
    params: {
      agreementNo,
      agreementVehicleId,
    },
    body,
  });
  console.log(`\n HERE IS response CREATE INSPECTION:: ${JSON.stringify(response)} \n`);
  // if (response.status !== 200) {
  //   throw new Error(response.body.desc);
  // }
  revalidatePath(currentRoute);
  return response;
}

export async function updateInspection({
  agreementNo,
  agreementVehicleId,
  inspectionId,
  odometerReading,
  inspectionType,
  fuelLevel,
  replacement,
  vehicleStatus,
  branchId,
}: {
  agreementNo: string;
  agreementVehicleId: number;
  inspectionId: number;
  odometerReading?: number;
  inspectionType: number;
  branchId: number | string;
  fuelLevel?: number;
  replacement?: boolean;
  vehicleStatus?: string;
}) {
  const currentRoute = replacement ? `/rental/branches/${branchId}/replace-vehicle/${agreementNo}/inspection-details` : `/rental/branches/${branchId}/close-agreements/${agreementNo}/inspection-details`;
 
  const body = {
      inspectionType, // checkin type
      metadata: {
        ...(odometerReading !== undefined && odometerReading !== null && { odometerReading }),
        ...(fuelLevel !== undefined && fuelLevel !== null && { fuelLevel }),
        replacement,
        vehicleStatus,
      },
    };
    console.log(`\n HERE IS body UPDATE INSPECTION:: ${JSON.stringify(body)} \n`);
  const response = await api.booking.updateVehicleInspection({
    params: {
      agreementNo,
      agreementVehicleId,
      inspectionId,
    },
    body,
  });
  console.log(`\n HERE IS response UPDATE INSPECTION:: ${JSON.stringify(response)} \n`);
  // if (response.status !== 200) {
  //   throw new Error(response.body.desc);
  // }
  revalidatePath(currentRoute);
  return response;
}

export async function waveOffExtraCharge(
  agreementNo: string,
  extraChargeType: string,
  waiveOff: boolean,
  branchId: string | number,
  replacement: boolean,
  reason?: string,
) {
  const currentRoute = replacement ? `/rental/branches/${branchId}/replace-vehicle/${agreementNo}/inspection-details` : `/rental/branches/${branchId}/close-agreements/${agreementNo}/inspection-details`;
 
  const response = await api.booking.waiveExtraCharge({
    params: {
      agreementNo,
    },
    body: {
      extraChargeType,
      waiveOff,
      waiveOffReason: reason,
    },
  });
  revalidatePath(currentRoute);
  return response;
}

export async function retryAuthorization(agreementNo: string, vehicleId: string, authType: string) {
  const response = await api.booking[authType === "TAJEER" ? "retryTajeerAuth" : "retryTammAuth"]({
    params: {
      agreementNo,
      vehicleId,
    },
    body: {},
  });
  return response;
}

export async function downloadAgreement(agreementNo: string) {
  const response = await api.booking.downloadAgreement({
    params: {
      agreementNo,
    },
  });
  return response;
}

export async function calculatePriceDiscount({
  bookingId,
  discountCode,
}: {
  bookingId: string;
  discountCode: string | null;
}) {
  try {
    const response = await api.bookingDetails.calculatePrice({
      params: {
        bookingId,
      },
      body: {
        discountCode: discountCode === "" ? "null" : discountCode,
      },
    });

    if ("code" in response.body) {
      return {
        error: response.body,
      };
    }

    return {
      data: response.body,
    };
  } catch (error) {
    console.error("Error calculating price:", error);
    return {
      error: {
        code: "UNKNOWN_ERROR",
        desc: "An unexpected error occurred while calculating the price",
      },
    };
  }
}

export async function updateBookingDiscount(bookingId: number, quoteId: string) {
  try {
    const response = await api.bookingDetails.updateBooking({
      params: {
        bookingId: Number(bookingId),
      },
      body: {
        newQuoteReference: quoteId ? String(quoteId) : "",
      },
    });

    return response;
  } catch (error) {
    console.error("Error updating booking:", error);
    throw error;
  }
}

export async function initVehicleReplacement(agreementNo: string, payload: IReplaceVehicle) {
  const response = await api.booking.initVehicleReplace({
    params: {
      agreementNo,
    },
    body: payload,
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n VHEICLE REPLACE INIT:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function closeExistingContract(
  agreementNo: string,
  agreementVehicleId: string,
  authType = "TAJEER",
  branchId: string
) {
  const response = await api.booking.closeAuthContract({
    params: {
      agreementNo,
      agreementVehicleId,
      authType,
    },
    query: {
      entityType: "AGREEMENT",
    },
  });

  revalidatePath(`/rental/branches/${branchId}/replace-vehicle/${agreementNo}/authorization`, "page");
  if (response.status !== 200) {
    return response;
  }
  console.log(`\n VHEICLE REPLACE INIT:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function initiateAuthContract(
  agreementNo: string,
  agreementVehicleId: string,
  authType = "TAJEER",
  branchId: string
) {
  const response = await api.booking.initiateAuthContract({
    params: {
      agreementNo,
      agreementVehicleId,
      authType,
    },
    body: {},
  });

  revalidatePath(`/rental/branches/${branchId}/replace-vehicle/${agreementNo}/authorization`, "page");
  if (response.status !== 200) {
    return response;
  }

  console.log(`\n VHEICLE REPLACE INIT:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function cancelAuthContract(agreementNo: string, authType = "TAJEER", branchId: string) {
  const response = await api.booking.cancelAuthContract({
    params: {
      agreementNo,
      authType,
    },
    query: {
      entityType: "AGREEMENT",
    },
    body: {
      cancellationReason: "replacement cancelled",
    },
  });

  revalidatePath(`/rental/branches/${branchId}/replace-vehicle/${agreementNo}/authorization`, "page");
  if (response.status !== 200) {
    return response;
  }

  return response;
}

export async function verifyOTP(agreementNo: string, authType = "TAJEER", otp: string, branchId: string) {
  const response = await api.booking.confirmAuthorization({
    params: {
      agreementNo,
      authType,
    },
    query: {
      entityType: "AGREEMENT",
    },
    body: {
      otp,
    },
  });

  if (response.status !== 200) {
    return response;
  }

  revalidatePath(`/rental/branches/${branchId}/replace-vehicle/${agreementNo}/authorization`, "page");
  return response;
}

export async function resendOTP(agreementNo: string, authType = "TAJEER") {
  const response = await api.booking.resendOTP({
    params: {
      agreementNo,
      authType,
    },
    query: {
      entityType: "AGREEMENT",
    },
    body: {},
  });

  if (response.status !== 200) {
    return response;
  }

  return response;
}

export const manualTajeer = async (bookingNo: string, existingContractNumber: string, branchId: string, agreementNo: string) => {
  const response = await api.tajeer.initiateTajeer({
    params: {
      bookingNo,
    },
    body: {
      existingContractNumber
    },
  });

  revalidatePath(`/rental/branches/${branchId}/replace-vehicle/${agreementNo}/authorization`, "page");
  return response;
};

export async function confirmVehicleReplacement(agreementNo: string, vehicleStatus: string) {
  const response = await api.booking.confirmVehicleReplace({
    params: {
      agreementNo,
    },
    body: {
      replacedVehicleNextStatus: vehicleStatus,
    },
  });

  if (response.status !== 200) {
    return response;
  }

  return response;
}

export async function getReplacementHistory(agreementNo: string) {
  const response = await api.booking.fetchReplacementHistory({
    query: {
      agreementNo,
      statuses: "REPLACED",
    },
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n REPLCED HISTORY:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function getVehicleDetails(plateNo: string) {
  const response = await api.availability.getVehicleDetailsV3({
    query: {
      plateNo,
      requireOpsData: true
    },
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n VHEICLE REPLACE INIT:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function getVehicleById(id: number) {
  const response = await api.availability.fetchVehicleById({
    params: {
      id,
    },
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n VHEICLE REPLACE INIT:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function getAgreementDetail(agreementNo: string) {
  const response = await api.booking.getAgreement({
    params: {
      agreementNo,
    },
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n REPLCED HISTORY:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function calculateAgreementPrice(agreementNo: string) {
  const response = await api.bookingDetails.calculateAgreementPrice({
    params: {
      agreementNo,
    },
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n REPLCED HISTORY:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function getVehicleInspectionDetails(agreementVehicleId: number) {
  const response = await api.booking.getVehicleInspectionDetails({
    params: {
      agreementVehicleId,
    },
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n REPLCED HISTORY:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function getBranchList() {
  const response = await api.branch.getDetailedBranchList();

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n REPLCED HISTORY:: ${JSON.stringify(response)} \n`);
  return response;
}

export async function fetchAvailableVehicles(bookingId: number, replacement: boolean, branchId: number) {
  const response = await api.suggestedVehicles.getAvailableVehicles({
    params: {
      bookingId,
    },
    query: {
      replacement,
      branchId,
    },
  });

  if (response.status !== 200) {
    return response;
  }
  console.log(`\n Available Vehicles:: ${JSON.stringify(response)} \n`);
  return response;
}
