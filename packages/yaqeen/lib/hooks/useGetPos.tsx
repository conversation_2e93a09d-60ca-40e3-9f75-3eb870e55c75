import { useCustomQuery } from './use-query';
import { CUSTOM_QUERY_STALE_TIME } from '../constant';

interface UseGetPosProps {
  branchId: number | string;
  enabled?: boolean;
}

interface PosData {
  total: number;
  data: { posId: string }[];
}

export const useGetPos = ({ branchId, enabled = true }: UseGetPosProps) => {
  return useCustomQuery<PosData>(
    ['pos', String(branchId)],
    `/next-api/pos?branchId=${encodeURIComponent(String(branchId))}`,
    {
      enabled: enabled && !!branchId,
      staleTime: CUSTOM_QUERY_STALE_TIME
    }
  );
};