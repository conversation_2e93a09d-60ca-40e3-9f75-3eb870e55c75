"use client";

import { useMemo } from "react";

import { type Vehicle } from "@/app/(portal)/rental/branches/[id]/bookings/[bookingId]/assign-a-vehicle/_components/vehicle-card";
import { type SelectedVehicleState } from "@/app/(portal)/rental/branches/[id]/bookings/[bookingId]/assign-a-vehicle/atoms";

export function useConsolidatedVehicleProperties({
  baseProperties,
  preSelectedVehicle,
  selectedVehicleState,
  bookingId,
  freeUpgradeData,
}: {
  baseProperties: Record<string, string | number | boolean | null>;
  preSelectedVehicle: Vehicle | null | undefined;
  selectedVehicleState: SelectedVehicleState | null;
  bookingId: number;
  freeUpgradeData: { reason: string; reasonText?: string | undefined } | null;
}) {
  const trackingProperties = useMemo(() => {
    const preModel = preSelectedVehicle?.model;
    const actualVehicle = selectedVehicleState?.[bookingId];
    const actualModel = actualVehicle?.model;

    return {
      ...baseProperties,
      preassigned_car_model: preModel ? `${preModel.make?.name?.en} ${preModel.name?.en}` : null,
      preassigned_car_group: preModel?.groupResponse?.code,
      preassigned_car_tag: preSelectedVehicle?.preferenceType,
      preassigned_car_fuel: preSelectedVehicle?.fuelLevel,
      preassigned_car_km: preSelectedVehicle?.odometerReading,

      actual_car_model: actualModel ? `${actualModel.make?.name?.en} ${actualModel.name?.en}` : null,
      actual_car_tag: actualVehicle?.preferenceType,
      actual_car_group: actualModel?.groupResponse?.code,
      actual_car_fuel: actualVehicle?.fuelLevel,
      actual_car_km: actualVehicle?.odometerReading,

      upgrade_type:
        actualVehicle?.preferenceType === "UPGRADE" ? (freeUpgradeData ? "free_upgrade" : "paid_upgrade") : null,
      upgrade_reason: freeUpgradeData?.reason,
      upgrade_price_diff: actualVehicle?.offer?.bookingPriceDifference,
    };
  }, [baseProperties, preSelectedVehicle, selectedVehicleState, bookingId, freeUpgradeData]);

  return trackingProperties;
}
