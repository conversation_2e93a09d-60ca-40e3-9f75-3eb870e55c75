import { useMemo } from "react";
import { differenceInHours, differenceInYears, parseISO } from "date-fns";
import { type AgreementInvoice } from "@/api/contracts/schema";

export const useConsolidatedAgreementProperties = (agreement?: AgreementInvoice) => {
  const consolidatedProperties = useMemo(() => {
    if (!agreement) {
      return {};
    }

    const driverDob = agreement.driver?.dob ? parseISO(agreement.driver.dob) : null;
    const driverAge = driverDob ? differenceInYears(new Date(), driverDob) : undefined;

    const pickupDate = agreement.pickupDateTime ? new Date(agreement.pickupDateTime * 1000) : null;
    const dropOffDate = agreement.dropOffDateTime ? new Date(agreement.dropOffDateTime * 1000) : null;
    const rentalDuration = pickupDate && dropOffDate ? differenceInHours(dropOffDate, pickupDate) : undefined;

    return {
      booking_id: agreement.bookingId,
      agreement_id: agreement.agreementNo,
      booking_status: agreement.status,
      booking_type: agreement.metadata?.bookingType || agreement.bookingType,
      booking_source: agreement.source,
      actual_pickup_branch_id: agreement.pickupBranchId,
      actual_pickup_branch_name: agreement.pickupBranch?.name?.en,
      actual_pickup_date: pickupDate?.toISOString(),
      actual_dropoff_branch_id: agreement.dropOffBranchId,
      actual_dropoff_branch_name: agreement.dropOffBranch?.name?.en,
      actual_dropoff_date: dropOffDate?.toISOString(),
      actual_car_group: agreement.assignedVehicle?.vehicleGroup?.code,
      actual_car_model: agreement.assignedVehicle?.model
        ? `${agreement.assignedVehicle.model.make?.name?.en || ""} ${agreement.assignedVehicle.model.name?.en || ""}`.trim()
        : null,
      paid: agreement.driverPaidAmount && parseFloat(agreement.driverPaidAmount) > 0 ? "yes" : "no",
      remaining_amount: agreement.remainingAmount,
      actual_rental_duration: rentalDuration,
      driver_name: agreement.driver?.name,
      driver_age: driverAge,
      driver_nationality: null, // Not available in AgreementInvoice type
      driver_document: null, // Not available in AgreementInvoice type
      actual_insurance: agreement.priceDetail?.insurances?.[0]?.name?.en,
      actual_addons:
        agreement.priceDetail?.addOns
          ?.map((addon) => addon.name?.en)
          .filter(Boolean)
          .join(", ") || null,
      discount_code: agreement.priceDetail?.discountDetail?.promoCode || null,
      authorisation_status: null, // Not available in AgreementInvoice type
    };
  }, [agreement]);

  return consolidatedProperties;
};
