import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

import { type TSelectItem } from "@/types";
import {
  format,
  isToday,
  isTomorrow,
  differenceInMinutes,
  differenceInHours,
  differenceInDays,
  differenceInMonths,
  isYesterday,
  isThisWeek,
  parse,
  differenceInYears,
  intervalToDuration,
} from "date-fns";
import { toZonedTime, format as formateZone } from "date-fns-tz";
import { PRIORITY_COUNTRIES, TIMEZONE } from "./constants";
import moment from "moment-hijri";
import { ar, arSA, enUS } from "date-fns/locale";
import type { TranslationValues } from "next-intl";
import { track } from "@amplitude/unified";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Override the `postformat` function to prevent numeric digit transformation
moment.updateLocale("en", {
  postformat: (value: string) => value, // Prevents numeral conversion to Arabic
});

/**
 -  next 6 hours - start from current time to next 6 hours
 *  today  - start from 12:00 am to 11:59 pm
 *  next 48 hours - start from current time to next 48 hours
 *  this week - start from saturday to friday
 *  this month  - start from 1st to last day of the month
 *  next 2 hours from current time
 */

export const getAllBookingTimeRange = (label = "") => {
  const now: Date = new Date();
  const start = new Date();
  const end = new Date();

  if (label === "") {
    return { start: 0, end: 0 };
  }

  switch (label) {
    case "NEXT_6_HOURS":
      start.setHours(now.getHours());
      end.setHours(now.getHours() + 6);
      break;
    case "TODAY":
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
    case "NEXT_48_HOURS":
      start.setHours(now.getHours());
      end.setHours(now.getHours() + 48);
      break;
    case "THIS_WEEK":
      // start from sturday to friday
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      const currentDay = start.getDay();
      const daysUntilFriday = 5 - currentDay;
      const daysFromSaturday = 6 - currentDay;
      start.setDate(start.getDate() - daysUntilFriday);
      end.setDate(end.getDate() + daysFromSaturday);
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
    case "THIS_MONTH":
      // start from 1st to last day of the month
      start.setDate(1);
      start.setHours(0, 0, 0, 0);
      end.setMonth(end.getMonth() + 1);
      end.setDate(0);
      end.setHours(23, 59, 59, 999);
      break;
    case "NEXT_2_HOURS":
      start.setHours(now.getHours());
      end.setHours(now.getHours() + 2);
      break;
    default:
      start.setHours(now.getHours());
      break;
  }

  const startEpoch = Number((start.getTime() / 1000).toFixed(0));
  const endEpoch = Number((end.getTime() / 1000).toFixed(0));
  return { start: startEpoch, end: endEpoch };
};

/**
 -  next 6 hours - start from current time to next 6 hours
 *  today  - start from current to 11:59 pm
 *  next 48 hours - start from current time to next 48 hours
 *  this week - start from current day to friday
 *  this month  - start from current day of the month to last day of the month
 *  next 2 hours - start from current time to next 2 hours
 */

export const getUpcomingTimeRange = (label = "") => {
  const now = toZonedTime(new Date(), TIMEZONE);
  const start = toZonedTime(new Date(), TIMEZONE);
  const end = toZonedTime(new Date(), TIMEZONE);
  const startHours = now.getHours() - 2;

  switch (label) {
    case "NEXT_6_HOURS":
      start.setHours(startHours);
      end.setHours(now.getHours() + 6);
      break;
    case "TODAY":
      start.setHours(startHours);
      end.setHours(23, 59, 59, 999);
      break;
    case "NEXT_48_HOURS":
      start.setHours(startHours);
      end.setHours(now.getHours() + 48);
      break;
    case "THIS_WEEK":
      // start from sturday to friday
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      const currentDay = start.getDay();
      const daysUntilFriday = 5 - currentDay;
      const daysFromSaturday = 6 - currentDay;
      start.setDate(start.getDate() - daysUntilFriday);
      end.setDate(end.getDate() + daysFromSaturday);
      start.setHours(0, 0, 0, 0);
      end.setHours(23, 59, 59, 999);
      break;
    case "THIS_MONTH":
      // start from 1st to last day of the month
      start.setDate(1);
      start.setHours(0, 0, 0, 0);
      end.setMonth(end.getMonth() + 1);
      end.setDate(0);
      end.setHours(23, 59, 59, 999);
      break;
    case "NEXT_2_HOURS":
      start.setHours(startHours);
      end.setHours(now.getHours() + 2);
      break;
    default:
      start.setHours(startHours);
      break;
  }

  const startEpoch = Number((start.getTime() / 1000).toFixed(0));
  const endEpoch = Number((end.getTime() / 1000).toFixed(0));
  return { start: startEpoch, end: label ? endEpoch : 0 };
};

const arWordMap = {
  minute: "دقيقة",
  minutes: "دقائق",
  hour: "ساعة",
  hours: "ساعات",
  yesterday: "أمس",
  today: "اليوم",
  tomorrow: "غدًا",
  passed: "مضى",
  in: "في",
};

const enWordMap = {
  minute: "minute",
  minutes: "minutes",
  hour: "hour",
  hours: "hours",
  yesterday: "yesterday",
  today: "today",
  tomorrow: "tomorrow",
  passed: "passed",
  in: "in",
};

export const formattedPickupTime = (epochTime = 0, status = "", locale = arSA) => {
  const now: Date = new Date();
  const pickupDate: Date = new Date(epochTime * 1000);
  const formattedDate: string = format(pickupDate, "dd, MMMM yyyy", { locale });
  const formattedTime: string = format(pickupDate, "HH:mm", { locale });
  const timeDifference: number = differenceInMinutes(pickupDate, now);
  const hoursDifference: number = differenceInHours(pickupDate, now);
  const daysDifference = differenceInDays(pickupDate, now);

  const wordMap = locale === arSA ? arWordMap : enWordMap;
  const isOutSideOngoingNupcoming = status && !["UPCOMING", "ONGOING"].includes(status.toUpperCase());

  let result = {
    formattedString: formattedDate,
    displayText: formattedTime,
    colorClass: "text-slate-900",
  };

  // if days difference is more than 1 day back then show the date
  if (daysDifference < -1) {
    // Default result is already set
  }
  // if pickup date is yesterday, then show yesterday and time
  else if (daysDifference === -1) {
    result = {
      formattedString: `${wordMap.yesterday} - ${formattedTime}`,
      displayText: "",
      colorClass: "text-slate-900",
    };
  } else if (isToday(pickupDate)) {
    result.formattedString = `${wordMap.today} - ${formattedTime}`;

    if (timeDifference < 0) {
      // if less than one hour passed, show minutes
      if (timeDifference >= -60) {
        const minuteText = Math.abs(timeDifference) === 1 ? wordMap.minute : wordMap.minutes;
        result.displayText = `${Math.abs(timeDifference)} ${minuteText} ${wordMap.passed}`;
        result.colorClass = "text-red-700";
        // if more than one hour passed, show hours
      } else {
        const hourText = Math.abs(hoursDifference) === 1 ? wordMap.hour : wordMap.hours;
        result.displayText = `${Math.abs(hoursDifference)} ${hourText} ${wordMap.passed}`;
        result.colorClass = "text-slate-500";
      }
    } else {
      // if coming in less than 1 hour, then show minutes
      if (timeDifference < 60) {
        const minuteText = timeDifference === 1 ? wordMap.minute : wordMap.minutes;
        result.displayText = `${wordMap.in} ${timeDifference} ${minuteText}`;
        result.colorClass = "text-green-700";
        // if more than 1 hour, then show hours
      } else {
        const hourText = hoursDifference === 1 ? wordMap.hour : wordMap.hours;
        result.displayText = `${wordMap.in} ${hoursDifference} ${hourText}`;
        result.colorClass = "text-slate-900";
      }
    }
  } else if (isTomorrow(pickupDate)) {
    result = {
      formattedString: `${wordMap.tomorrow} - ${formattedTime}`,
      displayText: "",
      colorClass: "text-slate-900",
    };
    // if hours difference is less than 7 days then show the day
  } else if (daysDifference <= 7) {
    result = {
      formattedString: `${format(pickupDate, "EEEE", { locale })} - ${formattedTime}`,
      displayText: "",
      colorClass: "text-slate-900",
    };
  }

  if (isOutSideOngoingNupcoming) {
    result.displayText = "";
  }

  return result;
};

/**
 * Similar to formattedPickupTime but without the relative time expressions
 * Returns only the formatted date string without phrases like "minutes passed" or "hours passed"
 */
export const formatSimplePickupTime = (epochTime = 0) => {
  const now: Date = new Date();
  const pickupDate: Date = new Date(epochTime * 1000);
  const formattedDate: string = format(pickupDate, "dd-MM-yyyy");
  const formattedTime: string = format(pickupDate, "HH:mm");
  const daysDifference = differenceInDays(pickupDate, now);

  let result = {
    formattedString: formattedDate,
    colorClass: "text-slate-900",
  };

  // If days difference is more than 1 day back then show the date
  if (daysDifference < -1) {
    // Default result is already set
  }
  // If pickup date is yesterday, then show yesterday and time
  else if (daysDifference === -1) {
    result = {
      formattedString: `Yesterday - ${formattedTime}`,
      colorClass: "text-slate-900",
    };
  } else if (isToday(pickupDate)) {
    result.formattedString = `Today - ${formattedTime}`;
  } else if (isTomorrow(pickupDate)) {
    result = {
      formattedString: `Tomorrow - ${formattedTime}`,
      colorClass: "text-slate-900",
    };
    // If hours difference is less than 7 days then show the day
  } else if (daysDifference <= 7) {
    result = {
      formattedString: `${format(pickupDate, "EEEE")} - ${formattedTime}`,
      colorClass: "text-slate-900",
    };
  }

  return result;
};

/*
 function which will take the epoch time and will gimme the time string based on following
 if hours diff is less equeal 1 hour, then show "On time"
 if hours diff is more than 1 hour, then show "x hours late"
  if hours diff is less than 0, then show "x hours early"

  if hours diff is more than 24 hours, then show "x days late"
  if hours diff is less than -24 hours then show "x days early" 

  if hours diff is more than 1 week, then show "x weeks late"
  if hours diff is less than -1 week then show "x weeks early"
 
*/

const arWordMap2 = {
  hour: "ساعة",
  hours: "ساعات",
  day: "يوم",
  days: "أيام",
  week: "أسبوع",
  weeks: "أسابيع",
  early: "مبكر",
  late: "متأخر",
};

const enWordMap2 = {
  hour: "hour",
  hours: "hours",
  day: "day",
  days: "days",
  week: "week",
  weeks: "weeks",
  early: "early",
  late: "late",
};

export const getTimeDifference = (epochTime: number, locale: string) => {
  const now: Date = new Date();
  const pickupDate: Date = new Date(epochTime * 1000);
  const hoursDifference: number = differenceInHours(now, pickupDate);
  const daysDifference = differenceInDays(now, pickupDate);

  const wordMap = locale === "ar" ? arWordMap2 : enWordMap2;
  if (hoursDifference === 0) {
    return "On time";
  } else if (hoursDifference > 0) {
    return `${hoursDifference} ${wordMap.hours} ${wordMap.late}`;
  } else if (hoursDifference < 0) {
    return `${Math.abs(hoursDifference)} ${wordMap.hours} ${wordMap.early}`;
  } else if (daysDifference > 1) {
    return `${daysDifference} ${wordMap.days} ${wordMap.late}`;
  } else if (daysDifference < -1) {
    return `${Math.abs(daysDifference)} ${wordMap.days} ${wordMap.early}`;
  } else if (daysDifference > 7) {
    return `${Math.floor(daysDifference / 7)} ${wordMap.weeks} ${wordMap.late}`;
  } else if (daysDifference < -7) {
    return `${Math.abs(Math.floor(daysDifference / 7))} ${wordMap.weeks} ${wordMap.early}`;
  } else {
    return "On time";
  }
};

export const getCurrentTimeMinusNHours = (n: number) => {
  const now: Date = new Date();
  const startTime = new Date();
  startTime.setHours(now.getHours() - n);
  const startEpoch = Number((startTime.getTime() / 1000).toFixed(0));
  return startEpoch;
};

/**
 * upcoming , color = blue-100
 * noshow , color = slate-200
 * ongoing , color = lumi-200
 * late , color = red-600
 * cancelled , color = red-100
 * completed , color = slate-600
 * suspended , color = orange-200
 */
export const getBadgeColor = (status = "") => {
  switch (status) {
    case "UPCOMING":
      return "bg-blue-100 hover:bg-blue-100/80 text-slate-900";
    case "NO_SHOW":
      return "bg-slate-200 hover:bg-slate-200/80 text-slate-900";
    case "ONGOING":
      return "bg-lumi-200 hover:bg-lumi-200/80 text-slate-900";
    case "LATE_RETURN":
      return "bg-red-600 hover:bg-red-600/80 text-white";
    case "CANCELLED":
      return "bg-red-100 hover:bg-red-100/80  text-slate-900";
    case "COMPLETED":
      return "bg-slate-900 hover:bg-slate-900/80 text-white";
    case "SUSPENDED":
      return "bg-orange-200 hover:bg-orange-200/80 text-slate-900";
    default:
      return "bg-blue-100 hover:bg-blue-100/80 text-slate-900";
  }
};

export const noop = (): void => void 0;

export const amountFormatter = (amount: number) => {
  // formate amout upto 2 decimal places and with commas
  return amount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

export const getFilterInParams = (params: [string, string][], filterArray: TSelectItem[]) => {
  return filterArray?.find((filter) => params.some(([key, _]: [string, string]) => key === filter.value));
};

export const toUnderscoreCap = (str: string) => {
  return str.toUpperCase().split(" ").join("_");
};

export const toUnderscoreLower = (str: string) => {
  return str.toLowerCase().split(" ").join("_");
};
export const toDashLower = (str: string) => {
  return str.toLowerCase().split(" ").join("-");
};

export const toNormal = (str: string) => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase().split("_").join(" ");
};

// conver string from pay_service_tax to payServiceTax
export const toCamelCase = (str: string) => {
  return str
    .split("_")
    .map((word, index) =>
      index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    )
    .join("");
};

export const buildQueryParams = (params: Record<string, string | number>) => {
  return Object.fromEntries(
    Object.entries(params).filter(([, value]) => value !== undefined && value !== null && value !== "")
  );
};

export function gregorianToHijri(dateString: string) {
  const hijriDate = moment(dateString, "DD-MM-YYYY").format("iD-iM-iYYYY");
  return hijriDate;
}
export function hijriToGregorian(dateString: string) {
  const date = moment(dateString, "iD-iM-iYYYY").format("DD-MM-YYYY");
  return date;
}

// if hijri date is not available, and greogrian date is available, then convert gregorian to hijri and return both
export const getFullDate = (gregorean = "", hijri = "") => {
  let res = {
    hijri,
    gregorean,
  };
  if (!hijri && gregorean) {
    res = {
      hijri: gregorianToHijri(gregorean),
      gregorean,
    };
  } else if (hijri && !gregorean) {
    res = {
      hijri,
      gregorean: hijriToGregorian(hijri),
    };
  }

  return res;
};

// Function to check if license expiry date meets minimum validity requirements
export const calculateAge = (dateOfBirth: string, minAge = 18, yearType = ""): boolean => {
  // Ensure we have a valid date string
  if (!dateOfBirth) return false;

  try {
    if (yearType === "hijri") {
      // Convert Hijri date to Gregorian date
      dateOfBirth = moment(dateOfBirth, "iDD/iMM/iYYYY").format("DD/MM/YYYY");
    } else {
      // Convert Gregorian date to moment format
      dateOfBirth = moment(dateOfBirth, "DD/MM/YYYY").format("DD/MM/YYYY");
    }
    const dob = moment(dateOfBirth, "DD/MM/YYYY");
    // Check if the date is valid
    if (!dob.isValid()) {
      console.warn("Invalid date format:", dateOfBirth);
      return false;
    }

    // Get today's date
    const today = moment();

    // Calculate the difference in years
    const age = today.diff(dob, "years");
    const ret = age >= minAge;
    // Return true if person is at least minAge years old
    console.warn("Age Check Result:", ret, "Age:", age, "DOB:", dob.format("DD/MM/YYYY"));
    return ret;
  } catch (error) {
    console.error("Error checking minimum age:", error);
    return false;
  }
};

// Function will take liscence expiry and will check if it is valid for next 6 months and more or not
export const checkLicenseExpiry = (date: string, minMonths: number, format: string) => {
  let value = date;

  value = date.split("/").reverse().join("-");

  const expiry = new Date(value);
  const today = new Date();

  // Calculate the difference in months
  const months = differenceInMonths(expiry, today);
  return months >= minMonths;
};

export const toSaudiZoned = (date: number) => {
  return Math.floor(new Date(date * 1000).getTime() / 1000);
};

export const isValidDate = (date: string) => {
  const [day, month, year] = date.split("/");
  if (day && month && year) {
    return true;
  }
  return false;
};

// Create a function which will get the date in this formate "dd-MM-yyyy" and will check if each part is less than 10 then it will make it 2 digit
const formatedDay = (day: number) => (day < 10 ? `0${day}` : day.toString());
export const makeDateValid = (date: string) => {
  if (!date) return "";
  // if date contains / then split with / else split with -
  if (date.includes("-")) {
    const [day = "", month = "", year = ""] = date.split("-");
    return `${formatedDay(parseInt(day))}-${formatedDay(parseInt(month))}-${formatedDay(parseInt(year))}`;
  }
  const [day = "", month = "", year = ""] = date.split("/");
  return `${formatedDay(parseInt(day))}-${formatedDay(parseInt(month))}-${formatedDay(parseInt(year))}`;
};

// formate date to dd/mm/yyyy
export const formateFromDateTo = (date: string, formate = "/") => {
  if (!date) return "";
  // if date contains / then split with / else split with -
  if (date.includes("-")) {
    const [day = "", month = "", year = ""] = date.split("-");
    return `${formatedDay(parseInt(day))}${formate}${formatedDay(parseInt(month))}${formate}${formatedDay(parseInt(year))}`;
  }
  const [day = "", month = "", year = ""] = date.split("/");
  return `${formatedDay(parseInt(day))}${formate}${formatedDay(parseInt(month))}${formate}${formatedDay(parseInt(year))}`;
};

export const toBase64 = (str: string) =>
  typeof window === "undefined" ? Buffer.from(str).toString("base64") : window.btoa(str);

export const shimmer = (w: number, h: number) => `
  <svg width="${w}" height="${h}" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
      <linearGradient id="g">
        <stop stop-color="#f6f7f8" offset="20%" />
        <stop stop-color="#edeef1" offset="50%" />
        <stop stop-color="#f6f7f8" offset="70%" />
      </linearGradient>
    </defs>
    <rect width="${w}" height="${h}" fill="#f6f7f8" />
    <rect id="r" width="${w}" height="${h}" fill="url(#g)" />
    <animate xlink:href="#r" attributeName="x" from="-${w}" to="${w}" dur="1s" repeatCount="indefinite"  />
  </svg>`;

export type CleaningTimeFormat = {
  displayText: string;
  category: "Today" | "Yesterday" | "Same week" | "More than a week";
};

export function formatCleaningTime(
  timestamp: number | null,
  t: (key: string, values?: TranslationValues) => string
): CleaningTimeFormat {
  if (!timestamp) {
    return {
      displayText: t("cleaning.neverCleaned"),
      category: "More than a week",
    };
  }

  const date = new Date(timestamp); // Timestamp is already in milliseconds
  const now = new Date();
  const minutesDiff = Math.abs(differenceInMinutes(date, now));
  const hoursDiff = Math.abs(differenceInHours(date, now));

  if (isToday(date)) {
    if (minutesDiff < 60) {
      return {
        displayText: t("cleaning.minutesAgo", { count: minutesDiff }),
        category: "Today",
      };
    }
    return {
      displayText: t("cleaning.hoursAgo", { count: hoursDiff }),
      category: "Today",
    };
  }

  if (isYesterday(date)) {
    return {
      displayText: t("cleaning.yesterday"),
      category: "Yesterday",
    };
  }

  if (isThisWeek(date)) {
    return {
      displayText: t("cleaning.onDay", { day: format(date, "EEEE") }),
      category: "Same week",
    };
  }

  return {
    displayText: t("cleaning.onDate", { date: format(date, "dd/MM/yyyy") }),
    category: "More than a week",
  };
}

export type InspectionTimeFormat = {
  displayText: string;
  category: "Today" | "Yesterday" | "Same week" | "More than a week";
};

export function formatInspectionTime(
  timestamp: number | null,
  t: (key: string, values?: TranslationValues) => string
): InspectionTimeFormat {
  if (!timestamp) {
    return {
      displayText: t("inspection.neverInspected"),
      category: "More than a week",
    };
  }
  const date = new Date(timestamp < 1e12 ? timestamp * 1000 : timestamp); // Convert to milliseconds if not already
  const now = new Date();
  const minutesDiff = Math.abs(differenceInMinutes(date, now));
  const hoursDiff = Math.abs(differenceInHours(date, now));

  if (isToday(date)) {
    if (minutesDiff < 60) {
      return {
        displayText: t("inspection.minutesAgo", { count: minutesDiff }),
        category: "Today",
      };
    }
    return {
      displayText: t("inspection.hoursAgo", { count: hoursDiff }),
      category: "Today",
    };
  }

  if (isYesterday(date)) {
    return {
      displayText: t("inspection.yesterday"),
      category: "Yesterday",
    };
  }

  if (isThisWeek(date)) {
    return {
      displayText: t("inspection.onDay", { day: format(date, "EEEE") }),
      category: "Same week",
    };
  }

  return {
    displayText: t("inspection.onDate", { date: format(date, "dd/MM/yyyy") }),
    category: "More than a week",
  };
}

export const plateNumberToArabicNumber = (plateNumber: string) => {
  return plateNumber.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[Number(d)] ?? d);
};

export const formatDateTime = (milliseconds: number): string => {
  const dtFormat = new Intl.DateTimeFormat("en-UK", {
    timeStyle: "medium",
    hour12: true,
    dateStyle: "short",
    // timeZone: "Asia/Riyadh",
  });

  return dtFormat.format(new Date(milliseconds * 1000));
};

/**
 * Format date with locale-aware day names
 */
export const formatLocalizedDate = (timestamp: number, locale: string): string => {
  const date = new Date(timestamp * 1000);
  // Use the appropriate locale object based on the current locale
  const dateLocale = locale === "ar" ? ar : enUS;

  return formateZone(toZonedTime(date, TIMEZONE), "EEEE, dd/MM/yyyy, HH:mm", {
    timeZone: TIMEZONE,
    locale: dateLocale,
  });
};
export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const MINUTE_OPTIONS = Array.from({ length: 48 }).map((_, i) => {
  const hour = Math.floor(i / 2)
    .toString()
    .padStart(2, "0");
  const minute = i % 2 === 0 ? "00" : "30";
  const time = `${hour}:${minute}`;
  return {
    value: time,
    label: time,
  };
});

export const englishToArabicLetters: Record<string, string> = {
  A: "ا",
  B: "ب",
  C: "ج",
  D: "د",
  E: "ع",
  F: "ف",
  G: "ق",
  H: "ه",
  I: "ع",
  J: "ح",
  K: "ك",
  L: "ل",
  M: "م",
  N: "ن",
  O: "و",
  P: "ب",
  Q: "ق",
  R: "ر",
  S: "س",
  T: "ط",
  U: "و",
  V: "ى",
  W: "و",
  X: "ص",
  Y: "ي",
  Z: "م",
};

export const convertPlateToArabic = (plate: string): string => {
  return plate
    .split("")
    .map((char) => {
      if (/\d/.test(char)) {
        return "٠١٢٣٤٥٦٧٨٩"[Number(char)];
      }
      const upperChar = char.toUpperCase();
      if (englishToArabicLetters[upperChar]) {
        return englishToArabicLetters[upperChar];
      }
      return char;
    })
    .join("");
};

export const convertPlateToArabicFormat = (plate: string): string => {
  // Separate letters and numbers
  const letters = plate.split("").filter((char) => /[a-zA-Z]/.test(char));
  const numbers = plate.split("").filter((char) => /\d/.test(char));

  // Reverse only the alphabetic characters
  const reversedLetters = letters.reverse();

  // Convert letters to Arabic with spaces between them
  const arabicLetters = reversedLetters
    .map((char) => {
      const upperChar = char.toUpperCase();
      return englishToArabicLetters[upperChar] || char;
    })
    .join(" ");

  // Convert numbers to Arabic
  const arabicNumbers = numbers.map((char) => "٠١٢٣٤٥٦٧٨٩"[Number(char)] ?? char).join("");

  // Return in plateNoAr format: letters first, then numbers
  const parts = [];
  if (arabicLetters) parts.push(arabicLetters);
  if (arabicNumbers) parts.push(arabicNumbers);

  return parts.join(" ");
};

export const convertPlateToEnglish = (plate: string): string => {
  const arabicToEnglishLetters: Record<string, string> = {
    ا: "A",
    ب: "B",
    ج: "C",
    د: "D",
    ع: "E",
    ف: "F",
    ق: "G",
    ه: "H",
    ح: "J",
    ك: "K",
    ل: "L",
    م: "M",
    ن: "N",
    و: "O",
    ر: "R",
    س: "S",
    ط: "T",
    ى: "V",
    ص: "X",
    ي: "Y",
    ز: "Z",
  };

  return plate
    .split("")
    .map((char) => {
      if (/[٠-٩]/.test(char)) {
        return "٠١٢٣٤٥٦٧٨٩".indexOf(char).toString();
      }
      if (arabicToEnglishLetters[char]) {
        return arabicToEnglishLetters[char];
      }
      return char;
    })
    .join("");
};

// Function to sort countries by priority

// @ts-expect-error TODO: Fix this type error
export const sortCountriesByPriority = (countries) => {
  return [...countries].sort((a, b) => {
    const countryA = a.alpha2;
    const countryB = b.alpha2;

    // If both countries are in our priority list
    if (
      PRIORITY_COUNTRIES[countryA as keyof typeof PRIORITY_COUNTRIES] &&
      PRIORITY_COUNTRIES[countryB as keyof typeof PRIORITY_COUNTRIES]
    ) {
      return (
        PRIORITY_COUNTRIES[countryA as keyof typeof PRIORITY_COUNTRIES] -
        PRIORITY_COUNTRIES[countryB as keyof typeof PRIORITY_COUNTRIES]
      );
    }

    // If only first country is in priority list
    if (PRIORITY_COUNTRIES[countryA as keyof typeof PRIORITY_COUNTRIES]) {
      return -1;
    }

    // If only second country is in priority list
    if (PRIORITY_COUNTRIES[countryB as keyof typeof PRIORITY_COUNTRIES]) {
      return 1;
    }

    // Otherwise sort alphabetically by name
    return a.name.localeCompare(b.name);
  });
};

export function parseIfJsonLike(value: string | number | Record<string, boolean>): Record<string, string> | null {
  try {
    // eslint-disable-next-line @typescript-eslint/no-base-to-string
    const cleaned = value.toString().replace(/'/g, '"');
    const parsed = JSON.parse(cleaned);
    return typeof parsed === "object" && parsed !== null ? parsed : null;
  } catch {
    return null;
  }
}

export function isEmail(value: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}
// Format status string - remove underscores and convert to camelCase like "noShow"
export const formatToCamel = (status: string) => {
  if (!status) return "";

  // Remove underscores and split by them or spaces
  const parts = status.toLowerCase().split(/[_\s]+/);

  // First part stays as is, subsequent parts are capitalized for camelCase
  return parts
    .map((part, index) => {
      if (index === 0) return part;
      return part.charAt(0).toUpperCase() + part.slice(1);
    })
    .join("");
};

export const parseDate = (date: string, formatType: string) => {
  const parsedDate = parse(date, "dd-MM-yyyy", new Date());
  return format(parsedDate, formatType ?? "dd MMMM yyyy");
};

export const formatDate = (timeStamp: number, formatType: string) => {
  return format(new Date(timeStamp * 1000), formatType ?? "dd MMMM yyyy");
};

export const parseDateFromSlashFormat = (date: string) => {
  const parsedDate = parse(date, "dd/MM/yyyy", new Date());
  return format(parsedDate, "dd MMMM yyyy");
};

export const trackEvent = (eventName: string, eventProperties: Record<string, unknown>) => {
  console.log(`** gtm event ${eventName}: `, eventProperties);
  track(eventName, eventProperties);
};

export const getLastPathSegment = (pathname: string): string => {
  const segments = pathname.split("/").filter(Boolean); // removes empty strings
  const last = segments[segments.length - 1] || "";

  return last === "bookings" ? "all-bookings" : last;
};

export const calculateAgeInYears = (dob: string) => {
  const date = parse(dob, "dd-MM-yyyy", new Date());
  const age = differenceInYears(new Date(), date);
  return age;
};

// Remove leading/trailing slashes and replace internal slashes with underscores
export function convertPathToKey(path: string): string {
  return path
    .trim()
    .replace(/^\/+|\/+$/g, "")
    .replace(/\//g, "_");
}

// returns hours and days from secs
export function getFromSeconds(soldDaysInSeconds: number): { days: number; hours: number } {
  const totalHours = Math.floor(soldDaysInSeconds / 3600);
  const days = Math.floor(totalHours / 24);
  const hours = totalHours % 24;

  return { days, hours };
}
